#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهات المحسنة
اختبار شامل للواجهات المطورة والمكونات الجديدة
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_components():
    """اختبار المكونات المحسنة"""
    print("🔍 اختبار المكونات المحسنة...")
    
    try:
        from src.utils.themes import theme_manager
        from src.gui.components import ModernCard, StatCard, ModernEntry, ModernCombobox, ModernTreeview, ModernButton, ConfirmDialog
        from src.utils.rtl_support import rtl_support
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار الواجهات المحسنة")
        root.geometry("1200x800")
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure((0, 1), weight=1)
        main_frame.rowconfigure((1, 2), weight=1)
        
        # شريط الأدوات العلوي
        toolbar_frame = ttk.Frame(main_frame, style='Sidebar.TFrame', padding="15")
        toolbar_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        toolbar_frame.columnconfigure(1, weight=1)
        
        # العنوان
        title_frame = ttk.Frame(toolbar_frame)
        title_frame.grid(row=0, column=0, sticky=tk.E)
        
        icon_label = rtl_support.create_rtl_label(title_frame, text="🧪", font=("Arial", 20))
        icon_label.grid(row=0, column=0, padx=(0, 10))
        
        title_label = rtl_support.create_rtl_label(title_frame, text="اختبار الواجهات المحسنة", style='Title.TLabel')
        title_label.grid(row=0, column=1)
        
        # أزرار تغيير الثيم
        theme_frame = ttk.Frame(toolbar_frame)
        theme_frame.grid(row=0, column=1, padx=(20, 0))
        
        def change_theme(theme_name):
            theme_manager.set_theme(theme_name)
            theme_manager.apply_theme_to_style(style)
        
        themes = [("افتراضي", "default"), ("داكن", "dark"), ("أزرق", "blue"), ("أخضر", "green")]
        for i, (name, theme) in enumerate(themes):
            ModernButton(theme_frame, text=name, command=lambda t=theme: change_theme(t), 
                        style="TButton").grid(row=0, column=i, padx=2)
        
        # بطاقات الإحصائيات
        stats_frame = ttk.Frame(main_frame)
        stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        stats_frame.columnconfigure((0, 1, 2, 3), weight=1)
        
        # بطاقات إحصائية
        stat1 = StatCard(stats_frame, title="إجمالي الطلاب", value=150, icon="🎓", color="success")
        stat1.grid(row=0, column=0, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        stat2 = StatCard(stats_frame, title="غيابات اليوم", value=12, icon="📝", color="warning")
        stat2.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        stat3 = StatCard(stats_frame, title="طلاب متغيبون", value=8, icon="👥", color="danger")
        stat3.grid(row=0, column=2, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        stat4 = StatCard(stats_frame, title="نسبة الحضور", value="94%", icon="📊", color="info")
        stat4.grid(row=0, column=3, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # النموذج المحسن
        form_card = ModernCard(main_frame, title="📝 نموذج اختبار")
        form_card.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        form_content = form_card.content_frame
        form_content.columnconfigure(0, weight=1)
        
        # حقول الإدخال المحسنة
        name_entry = ModernEntry(form_content, "اسم الطالب", placeholder="ادخل اسم الطالب...")
        name_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        grade_combo = ModernCombobox(form_content, "الصف الدراسي", 
                                   values=["الأول متوسط", "الثاني متوسط", "الثالث متوسط"])
        grade_combo.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        section_combo = ModernCombobox(form_content, "الشعبة", values=["أ", "ب", "ج", "د"])
        section_combo.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        phone_entry = ModernEntry(form_content, "رقم الهاتف", placeholder="05xxxxxxxx")
        phone_entry.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # أزرار النموذج
        form_buttons = ttk.Frame(form_content)
        form_buttons.grid(row=4, column=0, pady=(10, 0), sticky=tk.E)
        
        def test_confirm():
            dialog = ConfirmDialog(root, "تأكيد", "هل تريد حفظ البيانات؟")
            result = dialog.show()
            if result:
                print("تم التأكيد")
            else:
                print("تم الإلغاء")
        
        ModernButton(form_buttons, text="💾 حفظ", command=test_confirm, 
                    style="Success.TButton").grid(row=0, column=0, padx=(0, 5))
        ModernButton(form_buttons, text="🧹 مسح", command=lambda: print("مسح"), 
                    style="TButton").grid(row=0, column=1)
        
        # الجدول المحسن
        table_card = ModernCard(main_frame, title="📋 جدول اختبار")
        table_card.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        
        # إنشاء جدول مع بحث
        columns = ['الاسم', 'الصف', 'الشعبة', 'الهاتف']
        table = ModernTreeview(table_card.content_frame, columns)
        table.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_card.content_frame.columnconfigure(0, weight=1)
        table_card.content_frame.rowconfigure(0, weight=1)
        
        # بيانات تجريبية
        test_data = [
            ['أحمد محمد علي', 'الأول متوسط', 'أ', '0501234567'],
            ['فاطمة سالم أحمد', 'الثاني متوسط', 'ب', '0507654321'],
            ['عبدالله يوسف محمد', 'الثالث متوسط', 'أ', '0509876543'],
            ['مريم عبدالرحمن', 'الأول متوسط', 'ج', '0502468135'],
            ['خالد عبدالعزيز', 'الثاني متوسط', 'أ', '0508642097'],
            ['نورا أحمد سالم', 'الثالث متوسط', 'ب', '0503691472'],
            ['محمد عبدالله', 'الأول متوسط', 'د', '0505283749'],
            ['سارة محمد علي', 'الثاني متوسط', 'ج', '0507418529']
        ]
        
        table.set_data(test_data)
        
        # تطبيق RTL
        rtl_support.apply_rtl_to_window(root)
        
        print("✅ تم إنشاء نافذة اختبار الواجهات المحسنة بنجاح")
        print("📝 تحقق من:")
        print("   - البطاقات الإحصائية التفاعلية")
        print("   - النماذج المحسنة مع placeholder")
        print("   - الجدول مع البحث المدمج")
        print("   - أزرار تغيير الثيم")
        print("   - نوافذ التأكيد")
        print("   - دعم RTL كامل")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهات المحسنة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_theme_switching():
    """اختبار تبديل الثيمات"""
    print("\n🎨 اختبار تبديل الثيمات...")
    
    try:
        from src.utils.themes import theme_manager
        
        # اختبار الثيمات المتاحة
        themes = theme_manager.get_available_themes()
        print("الثيمات المتاحة:")
        for theme_id, theme_name in themes:
            print(f"  - {theme_name} ({theme_id})")
        
        # اختبار تغيير الثيم
        for theme_id, theme_name in themes:
            success = theme_manager.set_theme(theme_id)
            if success:
                current = theme_manager.get_current_theme()
                print(f"✅ تم تطبيق ثيم: {current['name']}")
            else:
                print(f"❌ فشل في تطبيق ثيم: {theme_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الثيمات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لاختبار الواجهات المحسنة"""
    print("🚀 بدء اختبار الواجهات المحسنة...")
    print("=" * 60)
    
    # اختبار الثيمات
    themes_ok = test_theme_switching()
    
    if themes_ok:
        print("\n🖥️ سيتم فتح نافذة اختبار الواجهات المحسنة...")
        print("تحقق من الميزات التالية:")
        print("1. البطاقات الإحصائية التفاعلية")
        print("2. النماذج المحسنة مع نصوص توضيحية")
        print("3. الجدول مع البحث المدمج")
        print("4. أزرار تغيير الثيم في الوقت الفعلي")
        print("5. نوافذ التأكيد المحسنة")
        print("6. دعم RTL كامل")
        print("7. تصميم حديث ومتجاوب")
        print("\nأغلق النافذة للمتابعة...")
        
        # اختبار الواجهات
        ui_ok = test_enhanced_components()
        
        if ui_ok:
            print("\n🎉 اختبار الواجهات المحسنة مكتمل!")
        else:
            print("\n⚠️ فشل في اختبار الواجهات المحسنة")
    else:
        print("\n⚠️ مشكلة في نظام الثيمات")

if __name__ == "__main__":
    main()
