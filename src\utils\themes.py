#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الثيمات والتصميم
يحتوي على ثيمات مختلفة وإعدادات التصميم للبرنامج
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class ThemeManager:
    """مدير الثيمات والتصميم"""
    
    def __init__(self):
        """تهيئة مدير الثيمات"""
        self.current_theme = "default"
        self.themes = self._load_themes()
        self.style = None
    
    def _load_themes(self):
        """تحميل الثيمات المتاحة"""
        return {
            "default": {
                "name": "الثيم الافتراضي",
                "colors": {
                    "primary": "#2E86AB",
                    "secondary": "#A23B72", 
                    "success": "#28A745",
                    "warning": "#FFC107",
                    "danger": "#DC3545",
                    "info": "#17A2B8",
                    "light": "#F8F9FA",
                    "dark": "#343A40",
                    "white": "#FFFFFF",
                    "background": "#FFFFFF",
                    "surface": "#F8F9FA",
                    "text_primary": "#212529",
                    "text_secondary": "#6C757D",
                    "border": "#DEE2E6",
                    "hover": "#E9ECEF"
                },
                "fonts": {
                    "family": "Tahoma",
                    "size_small": 10,
                    "size_normal": 12,
                    "size_large": 14,
                    "size_title": 16,
                    "size_header": 18
                }
            },
            "dark": {
                "name": "الثيم الداكن",
                "colors": {
                    "primary": "#0D6EFD",
                    "secondary": "#6F42C1",
                    "success": "#198754",
                    "warning": "#FFC107",
                    "danger": "#DC3545",
                    "info": "#0DCAF0",
                    "light": "#495057",
                    "dark": "#212529",
                    "white": "#FFFFFF",
                    "background": "#212529",
                    "surface": "#343A40",
                    "text_primary": "#FFFFFF",
                    "text_secondary": "#ADB5BD",
                    "border": "#495057",
                    "hover": "#495057"
                },
                "fonts": {
                    "family": "Tahoma",
                    "size_small": 10,
                    "size_normal": 12,
                    "size_large": 14,
                    "size_title": 16,
                    "size_header": 18
                }
            },
            "blue": {
                "name": "الثيم الأزرق",
                "colors": {
                    "primary": "#0056B3",
                    "secondary": "#004085",
                    "success": "#155724",
                    "warning": "#856404",
                    "danger": "#721C24",
                    "info": "#0C5460",
                    "light": "#E3F2FD",
                    "dark": "#1A237E",
                    "white": "#FFFFFF",
                    "background": "#F3F8FF",
                    "surface": "#E3F2FD",
                    "text_primary": "#1A237E",
                    "text_secondary": "#3F51B5",
                    "border": "#BBDEFB",
                    "hover": "#E1F5FE"
                },
                "fonts": {
                    "family": "Tahoma",
                    "size_small": 10,
                    "size_normal": 12,
                    "size_large": 14,
                    "size_title": 16,
                    "size_header": 18
                }
            },
            "green": {
                "name": "الثيم الأخضر",
                "colors": {
                    "primary": "#2E7D32",
                    "secondary": "#388E3C",
                    "success": "#1B5E20",
                    "warning": "#F57F17",
                    "danger": "#C62828",
                    "info": "#0277BD",
                    "light": "#E8F5E8",
                    "dark": "#1B5E20",
                    "white": "#FFFFFF",
                    "background": "#F1F8E9",
                    "surface": "#E8F5E8",
                    "text_primary": "#1B5E20",
                    "text_secondary": "#388E3C",
                    "border": "#C8E6C9",
                    "hover": "#E8F5E8"
                },
                "fonts": {
                    "family": "Tahoma",
                    "size_small": 10,
                    "size_normal": 12,
                    "size_large": 14,
                    "size_title": 16,
                    "size_header": 18
                }
            }
        }
    
    def set_theme(self, theme_name):
        """تعيين الثيم الحالي"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            return True
        return False
    
    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.themes[self.current_theme]
    
    def get_color(self, color_name):
        """الحصول على لون من الثيم الحالي"""
        theme = self.get_current_theme()
        return theme["colors"].get(color_name, "#000000")
    
    def get_font(self, size_name="normal"):
        """الحصول على خط من الثيم الحالي"""
        theme = self.get_current_theme()
        family = theme["fonts"]["family"]
        size = theme["fonts"].get(f"size_{size_name}", 12)
        return (family, size)
    
    def apply_theme_to_style(self, style):
        """تطبيق الثيم على ttk.Style"""
        self.style = style
        theme = self.get_current_theme()
        colors = theme["colors"]
        
        # تكوين الأنماط الأساسية
        style.theme_use('clam')
        
        # تكوين الألوان العامة
        style.configure('.',
                       background=colors["background"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"],
                       focuscolor=colors["primary"])
        
        # تكوين Label
        style.configure('TLabel',
                       background=colors["background"],
                       foreground=colors["text_primary"])
        
        # تكوين Button
        style.configure('TButton',
                       background=colors["surface"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"],
                       focuscolor=colors["primary"])
        
        style.map('TButton',
                 background=[('active', colors["hover"]),
                           ('pressed', colors["primary"])])
        
        # أزرار ملونة
        style.configure('Primary.TButton',
                       background=colors["primary"],
                       foreground=colors["white"])
        
        style.configure('Success.TButton',
                       background=colors["success"],
                       foreground=colors["white"])
        
        style.configure('Warning.TButton',
                       background=colors["warning"],
                       foreground=colors["dark"])
        
        style.configure('Danger.TButton',
                       background=colors["danger"],
                       foreground=colors["white"])
        
        # تكوين Entry
        style.configure('TEntry',
                       fieldbackground=colors["white"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"])
        
        # تكوين Combobox
        style.configure('TCombobox',
                       fieldbackground=colors["white"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"])
        
        # تكوين Frame
        style.configure('TFrame',
                       background=colors["background"],
                       bordercolor=colors["border"])
        
        # تكوين LabelFrame
        style.configure('TLabelframe',
                       background=colors["background"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"])
        
        style.configure('TLabelframe.Label',
                       background=colors["background"],
                       foreground=colors["text_primary"])
        
        # تكوين Treeview
        style.configure('Treeview',
                       background=colors["white"],
                       foreground=colors["text_primary"],
                       fieldbackground=colors["white"],
                       bordercolor=colors["border"])
        
        style.configure('Treeview.Heading',
                       background=colors["surface"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"])
        
        # تكوين Scrollbar
        style.configure('TScrollbar',
                       background=colors["surface"],
                       bordercolor=colors["border"],
                       arrowcolor=colors["text_primary"])
        
        # تكوين Progressbar
        style.configure('TProgressbar',
                       background=colors["primary"],
                       bordercolor=colors["border"])
        
        # أنماط مخصصة للعناوين
        style.configure('Title.TLabel',
                       background=colors["background"],
                       foreground=colors["primary"],
                       font=self.get_font("title") + ("bold",))
        
        style.configure('Header.TLabel',
                       background=colors["background"],
                       foreground=colors["text_primary"],
                       font=self.get_font("large") + ("bold",))
        
        style.configure('Info.TLabel',
                       background=colors["background"],
                       foreground=colors["text_secondary"],
                       font=self.get_font("normal"))
        
        # أنماط البطاقات
        style.configure('Card.TFrame',
                       background=colors["white"],
                       relief='solid',
                       borderwidth=1)
        
        style.configure('Card.TLabelframe',
                       background=colors["white"],
                       relief='solid',
                       borderwidth=1)
    
    def create_gradient_frame(self, parent, color1, color2, width=200, height=100):
        """إنشاء إطار بتدرج لوني"""
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        frame = tk.Frame(parent, bg=color1, width=width, height=height)
        return frame
    
    def get_available_themes(self):
        """الحصول على قائمة الثيمات المتاحة"""
        return [(name, theme["name"]) for name, theme in self.themes.items()]

# إنشاء مثيل عام للاستخدام
theme_manager = ThemeManager()
