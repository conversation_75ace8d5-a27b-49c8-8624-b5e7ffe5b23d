#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
تحتوي على القوائم الرئيسية والواجهة الأساسية
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
from datetime import datetime
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.database.database_manager import DatabaseManager
from src.utils.rtl_support import rtl_support
from src.utils.themes import theme_manager
from src.gui.components import ModernCard, StatCard, ModernButton, LoadingDialog
from src.gui.students_management import open_students_management
from src.gui.attendance_recording import open_attendance_recording
from src.gui.reports_window import open_reports_window
from src.gui.ai_insights import open_ai_insights
from src.gui.settings_window import open_settings_window
from src.gui.warnings_window import open_warnings_window

class MainWindow:
    """فئة النافذة الرئيسية"""
    
    def __init__(self, root):
        """تهيئة النافذة الرئيسية"""
        self.root = root
        self.db_manager = DatabaseManager()
        self.setup_window()
        self.create_menu()
        self.create_main_interface()
        self.load_dashboard_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        # إعداد النافذة
        self.root.title(Config.APP_NAME)
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        self.root.minsize(Config.WINDOW_MIN_WIDTH, Config.WINDOW_MIN_HEIGHT)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # إعداد الألوان والأنماط
        self.setup_styles()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        # الحصول على أفضل خط للعربية
        arabic_font = rtl_support.get_arabic_font()

        self.fonts = {
            'title': font.Font(family=arabic_font, size=Config.FONT_SIZE_TITLE, weight='bold'),
            'normal': font.Font(family=arabic_font, size=Config.FONT_SIZE_NORMAL),
            'small': font.Font(family=arabic_font, size=Config.FONT_SIZE_SMALL),
            'large': font.Font(family=arabic_font, size=Config.FONT_SIZE_LARGE)
        }
    
    def setup_styles(self):
        """إعداد الأنماط"""
        self.style = ttk.Style()

        # تطبيق الثيم
        theme_manager.apply_theme_to_style(self.style)

        # إعدادات إضافية مخصصة
        self.style.configure('Sidebar.TFrame',
                           background=theme_manager.get_color("surface"),
                           relief='solid', borderwidth=1)

        self.style.configure('Dashboard.TFrame',
                           background=theme_manager.get_color("background"))
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.on_closing)
        
        # قائمة الطلاب
        students_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الطلاب", menu=students_menu)
        students_menu.add_command(label="إدارة الطلاب", command=self.open_students_management)
        students_menu.add_command(label="إضافة طالب جديد", command=self.add_new_student)
        students_menu.add_command(label="البحث عن طالب", command=self.search_student)
        
        # قائمة الغياب
        attendance_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الغياب", menu=attendance_menu)
        attendance_menu.add_command(label="تسجيل الغياب", command=self.record_attendance)
        attendance_menu.add_command(label="عرض الغيابات", command=self.view_attendance)
        attendance_menu.add_command(label="تعديل سجل غياب", command=self.edit_attendance)
        attendance_menu.add_separator()
        attendance_menu.add_command(label="⚠️ إدارة الإنذارات", command=self.open_warnings)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير يومي", command=self.daily_report)
        reports_menu.add_command(label="تقرير شهري", command=self.monthly_report)
        reports_menu.add_command(label="تقرير فصلي", command=self.semester_report)
        reports_menu.add_separator()
        reports_menu.add_command(label="إحصائيات الغياب", command=self.absence_statistics)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="تصدير البيانات", command=self.export_data)
        tools_menu.add_command(label="استيراد البيانات", command=self.import_data)
        tools_menu.add_separator()
        tools_menu.add_command(label="إعدادات", command=self.open_settings)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # الشريط الجانبي (على اليمين في RTL)
        self.create_sidebar(main_frame)

        # المنطقة الرئيسية (على اليسار في RTL)
        self.create_main_area(main_frame)

        # تطبيق إعدادات RTL على النافذة
        rtl_support.apply_rtl_to_window(main_frame)

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_frame = ttk.Frame(parent, style='Sidebar.TFrame', padding="10")
        toolbar_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        toolbar_frame.columnconfigure(1, weight=1)

        # شعار وعنوان البرنامج
        title_frame = ttk.Frame(toolbar_frame)
        title_frame.grid(row=0, column=0, sticky=tk.E)

        app_icon = rtl_support.create_rtl_label(title_frame, text="🎓", font=("Arial", 20))
        app_icon.grid(row=0, column=0, padx=(0, 10))

        title_label = rtl_support.create_rtl_label(title_frame, text=Config.APP_NAME, style='Title.TLabel')
        title_label.grid(row=0, column=1)

        # أزرار سريعة
        quick_buttons_frame = ttk.Frame(toolbar_frame)
        quick_buttons_frame.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        quick_buttons = [
            ("🔄", self.refresh_dashboard, "تحديث"),
            ("🌙", self.toggle_theme, "تغيير الثيم"),
            ("❓", self.show_help, "مساعدة")
        ]

        for i, (icon, command, tooltip) in enumerate(quick_buttons):
            btn = ModernButton(quick_buttons_frame, text=icon, command=command, style="TButton")
            btn.grid(row=0, column=i, padx=2)
            # يمكن إضافة tooltip لاحقاً

        # معلومات المستخدم والوقت
        info_frame = ttk.Frame(toolbar_frame)
        info_frame.grid(row=0, column=2, sticky=tk.W)

        current_time = datetime.now().strftime("%H:%M")
        current_date = datetime.now().strftime("%Y-%m-%d")

        time_label = rtl_support.create_rtl_label(info_frame, text=f"🕐 {current_time}", style='Info.TLabel')
        time_label.grid(row=0, column=0, padx=(0, 10))

        date_label = rtl_support.create_rtl_label(info_frame, text=f"📅 {current_date}", style='Info.TLabel')
        date_label.grid(row=0, column=1)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي المحسن"""
        sidebar_frame = ttk.Frame(parent, style='Sidebar.TFrame', padding="15")
        # في RTL، الشريط الجانبي يكون على اليمين (column=1)
        sidebar_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        sidebar_frame.columnconfigure(0, weight=1)

        # عنوان الشريط الجانبي
        title_label = rtl_support.create_rtl_label(sidebar_frame, text="القوائم الرئيسية", style='Header.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 20), sticky=tk.E)

        # أزرار القوائم الرئيسية مع أيقونات
        buttons = [
            ("👥 إدارة الطلاب", self.open_students_management, "Primary.TButton", "إدارة بيانات الطلاب"),
            ("📝 تسجيل الغياب", self.record_attendance, "Success.TButton", "تسجيل الغياب اليومي"),
            ("⚠️ إدارة الإنذارات", self.open_warnings, "Danger.TButton", "إنذارات الطلاب المتغيبين"),
            ("📊 التقارير", self.view_reports, "TButton", "عرض التقارير والإحصائيات"),
            ("🤖 الذكاء الصناعي", self.absence_statistics, "Warning.TButton", "تحليلات ذكية"),
            ("⚙️ الإعدادات", self.open_settings, "TButton", "إعدادات البرنامج")
        ]

        for i, (text, command, style, tooltip) in enumerate(buttons):
            btn_frame = ttk.Frame(sidebar_frame)
            btn_frame.grid(row=i+1, column=0, pady=8, sticky=(tk.W, tk.E))
            btn_frame.columnconfigure(0, weight=1)

            btn = ModernButton(btn_frame, text=text, command=command, style=style)
            btn.grid(row=0, column=0, sticky=(tk.W, tk.E))

            # إضافة وصف صغير
            desc_label = rtl_support.create_rtl_label(btn_frame, text=tooltip, style='Info.TLabel')
            desc_label.grid(row=1, column=0, sticky=tk.E, pady=(2, 0))

        # إضافة مساحة فارغة
        spacer = ttk.Frame(sidebar_frame)
        spacer.grid(row=len(buttons)+1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        sidebar_frame.rowconfigure(len(buttons)+1, weight=1)

        # معلومات البرنامج في الأسفل
        info_frame = ttk.Frame(sidebar_frame)
        info_frame.grid(row=len(buttons)+2, column=0, sticky=(tk.W, tk.E), pady=(20, 0))

        version_label = rtl_support.create_rtl_label(info_frame, text=f"الإصدار {Config.APP_VERSION}", style='Info.TLabel')
        version_label.grid(row=0, column=0, sticky=tk.E)
    
    def create_main_area(self, parent):
        """إنشاء المنطقة الرئيسية"""
        # إطار المحتوى الرئيسي (على اليسار في RTL)
        self.main_content = ttk.Frame(parent)
        self.main_content.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        self.main_content.columnconfigure(0, weight=1)
        self.main_content.rowconfigure(0, weight=1)

        # إنشاء لوحة المعلومات
        self.create_dashboard()
    
    def create_dashboard(self):
        """إنشاء لوحة المعلومات المحسنة"""
        dashboard_frame = ttk.Frame(self.main_content, style='Dashboard.TFrame')
        dashboard_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        dashboard_frame.columnconfigure(0, weight=1)
        dashboard_frame.rowconfigure(2, weight=1)

        # عنوان لوحة المعلومات
        title_frame = ttk.Frame(dashboard_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(0, weight=1)

        title_label = rtl_support.create_rtl_label(title_frame, text="📊 لوحة المعلومات", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.E)

        # معلومات سريعة
        self.create_quick_info(dashboard_frame)

        # إحصائيات سريعة
        self.create_quick_stats(dashboard_frame)

        # الأنشطة الأخيرة
        self.create_recent_activities(dashboard_frame)

    def create_quick_info(self, parent):
        """إنشاء معلومات سريعة"""
        info_frame = rtl_support.create_rtl_labelframe(parent, text="معلومات سريعة", padding="10")
        info_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure((0, 1, 2, 3), weight=1)

        # في RTL، نبدأ من اليمين إلى اليسار
        # الوقت الحالي (أقصى اليمين)
        current_time = datetime.now().strftime("%H:%M")
        time_label = rtl_support.create_rtl_label(info_frame, text=f"الوقت: {current_time}", style='Info.TLabel')
        time_label.grid(row=0, column=0, padx=5)

        # الفصل الدراسي
        current_semester = Config.get_current_semester()
        semester_name = Config.SEMESTERS[current_semester]
        semester_label = rtl_support.create_rtl_label(info_frame, text=f"الفصل: {semester_name}", style='Info.TLabel')
        semester_label.grid(row=0, column=1, padx=5)

        # السنة الدراسية
        academic_year = Config.get_academic_year()
        year_label = rtl_support.create_rtl_label(info_frame, text=f"السنة الدراسية: {academic_year}", style='Info.TLabel')
        year_label.grid(row=0, column=2, padx=5)

        # التاريخ الحالي (أقصى اليسار)
        current_date = datetime.now().strftime("%Y-%m-%d")
        date_label = rtl_support.create_rtl_label(info_frame, text=f"التاريخ: {current_date}", style='Info.TLabel')
        date_label.grid(row=0, column=3, padx=5)

    def create_quick_stats(self, parent):
        """إنشاء إحصائيات سريعة محسنة"""
        stats_frame = ttk.Frame(parent)
        stats_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        stats_frame.columnconfigure((0, 1, 2, 3, 4), weight=1)

        # سيتم تحديث هذه القيم من قاعدة البيانات
        self.stats_cards = {}

        # في RTL، نرتب من اليمين إلى اليسار
        # الطلاب المتغيبون (أقصى اليمين)
        self.stats_cards['absent_students'] = StatCard(
            stats_frame,
            title="طلاب متغيبون",
            value=0,
            icon="👥",
            color="danger"
        )
        self.stats_cards['absent_students'].grid(row=0, column=0, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # الغيابات هذا الشهر
        self.stats_cards['month_absences'] = StatCard(
            stats_frame,
            title="غيابات الشهر",
            value=0,
            icon="📅",
            color="warning"
        )
        self.stats_cards['month_absences'].grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # الغيابات اليوم
        self.stats_cards['today_absences'] = StatCard(
            stats_frame,
            title="غيابات اليوم",
            value=0,
            icon="📝",
            color="info"
        )
        self.stats_cards['today_absences'].grid(row=0, column=2, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # الإنذارات النشطة
        self.stats_cards['active_warnings'] = StatCard(
            stats_frame,
            title="إنذارات نشطة",
            value=0,
            icon="⚠️",
            color="danger"
        )
        self.stats_cards['active_warnings'].grid(row=0, column=3, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # إجمالي الطلاب (أقصى اليسار)
        self.stats_cards['total_students'] = StatCard(
            stats_frame,
            title="إجمالي الطلاب",
            value=0,
            icon="🎓",
            color="success"
        )
        self.stats_cards['total_students'].grid(row=0, column=4, padx=5, sticky=(tk.W, tk.E, tk.N, tk.S))

    def create_recent_activities(self, parent):
        """إنشاء قائمة الأنشطة الأخيرة المحسنة"""
        activities_card = ModernCard(parent, title="🕒 الأنشطة الأخيرة")
        activities_card.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # إنشاء جدول الأنشطة مع دعم RTL
        columns = ('التاريخ', 'النشاط', 'التفاصيل')

        tree_frame = ttk.Frame(activities_card.content_frame)
        tree_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(1, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        activities_card.content_frame.columnconfigure(0, weight=1)
        activities_card.content_frame.rowconfigure(0, weight=1)

        self.activities_tree = rtl_support.create_rtl_treeview(tree_frame, columns=columns, show='headings', height=8)

        # تكوين الأعمدة مع محاذاة RTL
        column_widths = {'التاريخ': 120, 'النشاط': 150, 'التفاصيل': 200}
        for col in columns:
            self.activities_tree.heading(col, text=col, anchor='e')
            self.activities_tree.column(col, width=column_widths.get(col, 150), anchor='e')

        # شريط التمرير (على اليسار في RTL)
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.activities_tree.yview)
        self.activities_tree.configure(yscrollcommand=scrollbar.set)

        # ترتيب العناصر (شريط التمرير على اليسار)
        scrollbar.grid(row=0, column=0, sticky=(tk.N, tk.S))
        self.activities_tree.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # إضافة زر تحديث
        refresh_btn = ModernButton(activities_card.content_frame, text="🔄 تحديث",
                                 command=self.update_recent_activities, style="TButton")
        refresh_btn.grid(row=1, column=0, pady=(10, 0), sticky=tk.E)

    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            # تحديث الإحصائيات
            self.update_statistics()

            # تحديث الأنشطة الأخيرة
            self.update_recent_activities()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إجمالي الطلاب النشطين
            students = self.db_manager.get_students()
            total_students = len(students)
            self.stats_cards['total_students'].update_value(total_students)

            # غيابات اليوم
            today = datetime.now().strftime("%Y-%m-%d")
            today_absences = self.db_manager.get_daily_absences(today)
            self.stats_cards['today_absences'].update_value(len(today_absences))

            # إحصائيات الفصل الحالي
            current_semester_id = self.db_manager.get_current_semester_id()
            semester_stats = self.db_manager.get_absence_statistics(current_semester_id)

            self.stats_cards['month_absences'].update_value(semester_stats['total_absences'])
            self.stats_cards['absent_students'].update_value(semester_stats['absent_students'])

            # إحصائيات الإنذارات
            try:
                from src.utils.warnings_manager import WarningsManager
                warnings_manager = WarningsManager()
                warnings_stats = warnings_manager.get_warnings_statistics()
                active_warnings = warnings_stats.get('active_warnings', 0)
                self.stats_cards['active_warnings'].update_value(active_warnings)
            except Exception as e:
                print(f"خطأ في تحميل إحصائيات الإنذارات: {e}")
                self.stats_cards['active_warnings'].update_value(0)

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def update_recent_activities(self):
        """تحديث الأنشطة الأخيرة"""
        try:
            # مسح البيانات الحالية
            for item in self.activities_tree.get_children():
                self.activities_tree.delete(item)

            # الحصول على آخر الغيابات
            trends = self.db_manager.get_absence_trends(7)  # آخر 7 أيام

            for trend in trends[:10]:  # أحدث 10 أنشطة
                activity_type = Config.ABSENCE_TYPES.get(trend['absence_type'], trend['absence_type'])
                activity_text = f"تسجيل {activity_type}"
                details = f"{trend['count']} طالب/طالبة"

                self.activities_tree.insert('', 'end', values=(
                    trend['absence_date'],
                    activity_text,
                    details
                ))

        except Exception as e:
            print(f"خطأ في تحديث الأنشطة: {str(e)}")

    # ==================== دوال القوائم ====================

    def new_file(self):
        """إنشاء ملف جديد"""
        messagebox.showinfo("معلومات", "ميزة إنشاء ملف جديد قيد التطوير")

    def open_file(self):
        """فتح ملف"""
        messagebox.showinfo("معلومات", "ميزة فتح الملف قيد التطوير")

    def open_students_management(self):
        """فتح نافذة إدارة الطلاب"""
        try:
            open_students_management(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الطلاب: {str(e)}")

    def add_new_student(self):
        """إضافة طالب جديد"""
        messagebox.showinfo("معلومات", "نافذة إضافة طالب جديد قيد التطوير")

    def search_student(self):
        """البحث عن طالب"""
        messagebox.showinfo("معلومات", "نافذة البحث عن طالب قيد التطوير")

    def record_attendance(self):
        """تسجيل الغياب"""
        try:
            open_attendance_recording(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة تسجيل الغياب: {str(e)}")

    def view_attendance(self):
        """عرض الغيابات"""
        messagebox.showinfo("معلومات", "نافذة عرض الغيابات قيد التطوير")

    def edit_attendance(self):
        """تعديل سجل غياب"""
        messagebox.showinfo("معلومات", "نافذة تعديل الغياب قيد التطوير")

    def daily_report(self):
        """تقرير يومي"""
        messagebox.showinfo("معلومات", "التقرير اليومي قيد التطوير")

    def monthly_report(self):
        """تقرير شهري"""
        messagebox.showinfo("معلومات", "التقرير الشهري قيد التطوير")

    def semester_report(self):
        """تقرير فصلي"""
        messagebox.showinfo("معلومات", "التقرير الفصلي قيد التطوير")

    def view_reports(self):
        """عرض التقارير"""
        try:
            open_reports_window(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة التقارير: {str(e)}")

    def absence_statistics(self):
        """إحصائيات الغياب"""
        try:
            open_ai_insights(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة رؤى الذكاء الصناعي: {str(e)}")

    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("معلومات", "ميزة تصدير البيانات قيد التطوير")

    def import_data(self):
        """استيراد البيانات"""
        messagebox.showinfo("معلومات", "ميزة استيراد البيانات قيد التطوير")

    def open_warnings(self):
        """فتح نافذة إدارة الإنذارات"""
        try:
            open_warnings_window(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة الإنذارات: {str(e)}")

    def open_settings(self):
        """فتح نافذة الإعدادات"""
        try:
            open_settings_window(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة الإعدادات: {str(e)}")

    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم",
                          "برنامج تسجيل غياب الطلاب الذكي\n\n"
                          "الميزات الرئيسية:\n"
                          "• إدارة بيانات الطلاب\n"
                          "• تسجيل الغياب اليومي\n"
                          "• إنشاء التقارير المختلفة\n"
                          "• تحليل إحصائيات الغياب\n"
                          "• تصدير البيانات\n\n"
                          "للمساعدة، تواصل مع المطور")

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                          f"{Config.APP_NAME}\n"
                          f"الإصدار: {Config.APP_VERSION}\n"
                          f"المطور: {Config.APP_AUTHOR}\n\n"
                          "برنامج شامل لإدارة غياب طلاب المرحلة المتوسطة\n"
                          "يدعم الذكاء الصناعي لتحليل أنماط الغياب")

    def refresh_dashboard(self):
        """تحديث لوحة المعلومات"""
        loading = LoadingDialog(self.root, "جاري تحديث البيانات...")
        try:
            self.load_dashboard_data()
            loading.close()
            messagebox.showinfo("تم", "تم تحديث البيانات بنجاح")
        except Exception as e:
            loading.close()
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {str(e)}")

    def toggle_theme(self):
        """تغيير الثيم"""
        current_theme = theme_manager.current_theme
        themes = ["default", "dark", "blue", "green"]
        current_index = themes.index(current_theme)
        next_theme = themes[(current_index + 1) % len(themes)]

        theme_manager.set_theme(next_theme)
        theme_manager.apply_theme_to_style(self.style)

        messagebox.showinfo("تم", f"تم تغيير الثيم إلى: {theme_manager.get_current_theme()['name']}")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        🎓 برنامج تسجيل غياب الطلاب الذكي

        الاختصارات السريعة:
        • F1: مساعدة
        • F5: تحديث
        • Ctrl+N: طالب جديد
        • Ctrl+R: تقرير جديد

        للمساعدة الكاملة، راجع دليل المستخدم
        """
        messagebox.showinfo("مساعدة", help_text)

    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        if messagebox.askokcancel("خروج", "هل تريد إغلاق البرنامج؟"):
            try:
                self.db_manager.close_connection()
            except:
                pass
            self.root.destroy()
