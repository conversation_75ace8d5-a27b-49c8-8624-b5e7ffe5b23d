#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة رؤى الذكاء الصناعي
تعرض تحليلات وتنبؤات الذكاء الصناعي
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.ai.analytics import analytics_engine
from src.utils.rtl_support import rtl_support

class AIInsightsWindow:
    """فئة نافذة رؤى الذكاء الصناعي"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة رؤى الذكاء الصناعي"""
        self.parent = parent
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.insights_data = {}
        self.setup_window()
        self.create_interface()
        self.load_insights()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("رؤى الذكاء الصناعي")
        self.window.geometry("1400x900")
        self.window.minsize(1200, 800)
        
        # توسيط النافذة
        self.center_window()
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة رؤى الذكاء الصناعي"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure((0, 1), weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان
        arabic_font = rtl_support.get_arabic_font()
        title_label = rtl_support.create_rtl_label(main_frame, text="رؤى الذكاء الصناعي", font=(arabic_font, 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # منطقة الإحصائيات السريعة
        self.create_quick_stats(main_frame)

        # منطقة التحليلات والتنبؤات
        self.create_analysis_area(main_frame)

        # أزرار العمليات
        self.create_action_buttons(main_frame)

        # تطبيق إعدادات RTL
        rtl_support.apply_rtl_to_window(main_frame)
    
    def create_quick_stats(self, parent):
        """إنشاء منطقة الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(parent, text="إحصائيات سريعة", padding="15")
        stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        stats_frame.columnconfigure((0, 1, 2, 3), weight=1)
        
        # إنشاء تسميات الإحصائيات
        self.stats_labels = {}
        
        # إجمالي الغيابات المحللة
        self.stats_labels['total_analyzed'] = ttk.Label(stats_frame, text="الغيابات المحللة: 0", 
                                                       font=('Arial', 12, 'bold'))
        self.stats_labels['total_analyzed'].grid(row=0, column=0, padx=10, pady=5)
        
        # الطلاب المعرضون للخطر
        self.stats_labels['at_risk'] = ttk.Label(stats_frame, text="طلاب معرضون للخطر: 0", 
                                               font=('Arial', 12, 'bold'), foreground='red')
        self.stats_labels['at_risk'].grid(row=0, column=1, padx=10, pady=5)
        
        # دقة النموذج
        self.stats_labels['model_accuracy'] = ttk.Label(stats_frame, text="دقة النموذج: غير محدد", 
                                                       font=('Arial', 12))
        self.stats_labels['model_accuracy'].grid(row=0, column=2, padx=10, pady=5)
        
        # آخر تحديث
        self.stats_labels['last_update'] = ttk.Label(stats_frame, text="آخر تحديث: --", 
                                                    font=('Arial', 12))
        self.stats_labels['last_update'].grid(row=0, column=3, padx=10, pady=5)
    
    def create_analysis_area(self, parent):
        """إنشاء منطقة التحليلات والتنبؤات"""
        # إطار التحليلات
        analysis_frame = ttk.LabelFrame(parent, text="تحليل الأنماط", padding="10")
        analysis_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(1, weight=1)
        
        # منطقة التوصيات
        recommendations_frame = ttk.LabelFrame(analysis_frame, text="التوصيات", padding="10")
        recommendations_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        recommendations_frame.columnconfigure(0, weight=1)
        
        # قائمة التوصيات
        self.recommendations_text = tk.Text(recommendations_frame, height=6, wrap=tk.WORD, 
                                          font=('Arial', 10))
        recommendations_scrollbar = ttk.Scrollbar(recommendations_frame, orient=tk.VERTICAL, 
                                                command=self.recommendations_text.yview)
        self.recommendations_text.configure(yscrollcommand=recommendations_scrollbar.set)
        
        self.recommendations_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        recommendations_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # منطقة الأنماط
        patterns_frame = ttk.LabelFrame(analysis_frame, text="أنماط الغياب", padding="10")
        patterns_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        patterns_frame.columnconfigure(0, weight=1)
        patterns_frame.rowconfigure(0, weight=1)
        
        # جدول الأنماط
        columns = ('النمط', 'القيمة', 'النسبة', 'التفسير')
        self.patterns_tree = ttk.Treeview(patterns_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.patterns_tree.heading(col, text=col)
            self.patterns_tree.column(col, width=120, anchor='center')
        
        patterns_scrollbar = ttk.Scrollbar(patterns_frame, orient=tk.VERTICAL, 
                                         command=self.patterns_tree.yview)
        self.patterns_tree.configure(yscrollcommand=patterns_scrollbar.set)
        
        self.patterns_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        patterns_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # إطار الطلاب المعرضين للخطر
        risk_frame = ttk.LabelFrame(parent, text="الطلاب المعرضون للخطر", padding="10")
        risk_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        risk_frame.columnconfigure(0, weight=1)
        risk_frame.rowconfigure(0, weight=1)
        
        # جدول الطلاب المعرضين للخطر
        risk_columns = ('اسم الطالب', 'الصف', 'مستوى الخطر', 'الثقة', 'التاريخ المتوقع')
        self.risk_tree = ttk.Treeview(risk_frame, columns=risk_columns, show='headings', height=15)
        
        column_widths = {'اسم الطالب': 150, 'الصف': 80, 'مستوى الخطر': 100, 'الثقة': 80, 'التاريخ المتوقع': 120}
        for col in risk_columns:
            self.risk_tree.heading(col, text=col)
            self.risk_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        risk_scrollbar = ttk.Scrollbar(risk_frame, orient=tk.VERTICAL, command=self.risk_tree.yview)
        self.risk_tree.configure(yscrollcommand=risk_scrollbar.set)
        
        self.risk_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        risk_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(15, 0))
        
        # أزرار العمليات
        ttk.Button(buttons_frame, text="تحديث التحليل", command=self.load_insights, 
                  width=15).grid(row=0, column=0, padx=5)
        ttk.Button(buttons_frame, text="تدريب النموذج", command=self.train_model, 
                  width=15).grid(row=0, column=1, padx=5)
        ttk.Button(buttons_frame, text="تنبؤ مخصص", command=self.custom_prediction, 
                  width=15).grid(row=0, column=2, padx=5)
        ttk.Button(buttons_frame, text="تصدير التقرير", command=self.export_insights, 
                  width=15).grid(row=0, column=3, padx=5)
    
    def load_insights(self):
        """تحميل رؤى الذكاء الصناعي"""
        try:
            # عرض رسالة تحميل
            self.window.config(cursor="wait")
            self.window.update()
            
            # الحصول على تقرير الرؤى
            self.insights_data = analytics_engine.generate_insights_report()
            
            # تحديث الإحصائيات
            self.update_statistics()
            
            # تحديث التوصيات
            self.update_recommendations()
            
            # تحديث الأنماط
            self.update_patterns()
            
            # تحديث الطلاب المعرضين للخطر
            self.update_at_risk_students()
            
            # إخفاء رسالة التحميل
            self.window.config(cursor="")
            
            messagebox.showinfo("نجح", "تم تحديث التحليل بنجاح")
            
        except Exception as e:
            self.window.config(cursor="")
            messagebox.showerror("خطأ", f"خطأ في تحميل التحليل: {str(e)}")
    
    def update_statistics(self):
        """تحديث الإحصائيات السريعة"""
        try:
            if 'patterns_analysis' in self.insights_data:
                total_absences = self.insights_data['patterns_analysis'].get('total_absences', 0)
                self.stats_labels['total_analyzed'].config(text=f"الغيابات المحللة: {total_absences}")
            
            if 'at_risk_students' in self.insights_data:
                at_risk_count = len(self.insights_data['at_risk_students'])
                self.stats_labels['at_risk'].config(text=f"طلاب معرضون للخطر: {at_risk_count}")
            
            # آخر تحديث
            if 'generated_at' in self.insights_data:
                self.stats_labels['last_update'].config(text=f"آخر تحديث: {self.insights_data['generated_at']}")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
    
    def update_recommendations(self):
        """تحديث التوصيات"""
        try:
            self.recommendations_text.delete('1.0', tk.END)
            
            if 'patterns_analysis' in self.insights_data:
                recommendations = self.insights_data['patterns_analysis'].get('recommendations', [])
                
                if recommendations:
                    for i, recommendation in enumerate(recommendations, 1):
                        self.recommendations_text.insert(tk.END, f"{i}. {recommendation}\n\n")
                else:
                    self.recommendations_text.insert(tk.END, "لا توجد توصيات متاحة حالياً")
            
        except Exception as e:
            self.recommendations_text.insert(tk.END, f"خطأ في تحميل التوصيات: {str(e)}")
    
    def update_patterns(self):
        """تحديث أنماط الغياب"""
        try:
            # مسح البيانات الحالية
            for item in self.patterns_tree.get_children():
                self.patterns_tree.delete(item)
            
            if 'patterns_analysis' in self.insights_data:
                patterns = self.insights_data['patterns_analysis'].get('patterns', {})
                
                # أنماط الأيام
                if 'by_day' in patterns:
                    day_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
                    day_patterns = patterns['by_day']
                    total_days = sum(day_patterns.values()) if day_patterns else 1
                    
                    for day_num, count in day_patterns.items():
                        if day_num < len(day_names):
                            percentage = (count / total_days) * 100
                            interpretation = "عالي" if percentage > 20 else "طبيعي"
                            
                            self.patterns_tree.insert('', 'end', values=(
                                f"يوم {day_names[day_num]}",
                                count,
                                f"{percentage:.1f}%",
                                interpretation
                            ))
                
                # أنماط الشهور
                if 'by_month' in patterns:
                    month_names = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                    month_patterns = patterns['by_month']
                    total_months = sum(month_patterns.values()) if month_patterns else 1
                    
                    for month_num, count in month_patterns.items():
                        if month_num < len(month_names):
                            percentage = (count / total_months) * 100
                            interpretation = "ذروة" if percentage > 15 else "طبيعي"
                            
                            self.patterns_tree.insert('', 'end', values=(
                                f"شهر {month_names[month_num]}",
                                count,
                                f"{percentage:.1f}%",
                                interpretation
                            ))
            
        except Exception as e:
            print(f"خطأ في تحديث الأنماط: {str(e)}")
    
    def update_at_risk_students(self):
        """تحديث قائمة الطلاب المعرضين للخطر"""
        try:
            # مسح البيانات الحالية
            for item in self.risk_tree.get_children():
                self.risk_tree.delete(item)
            
            if 'at_risk_students' in self.insights_data:
                at_risk_students = self.insights_data['at_risk_students']
                
                for student in at_risk_students:
                    confidence_percent = f"{student['confidence']*100:.1f}%"
                    
                    # تلوين حسب مستوى الخطر
                    tags = ()
                    if student['risk_level'] == 'عالي':
                        tags = ('high_risk',)
                    elif student['risk_level'] == 'متوسط':
                        tags = ('medium_risk',)
                    
                    self.risk_tree.insert('', 'end', values=(
                        student['student_name'],
                        student['grade'],
                        student['risk_level'],
                        confidence_percent,
                        student['target_date']
                    ), tags=tags)
                
                # تكوين الألوان
                self.risk_tree.tag_configure('high_risk', background='#ffcccc')
                self.risk_tree.tag_configure('medium_risk', background='#fff2cc')
            
        except Exception as e:
            print(f"خطأ في تحديث الطلاب المعرضين للخطر: {str(e)}")
    
    def train_model(self):
        """تدريب نموذج التنبؤ"""
        try:
            self.window.config(cursor="wait")
            self.window.update()
            
            success = analytics_engine.train_prediction_model()
            
            self.window.config(cursor="")
            
            if success:
                messagebox.showinfo("نجح", "تم تدريب النموذج بنجاح")
                self.stats_labels['model_accuracy'].config(text="دقة النموذج: تم التدريب")
            else:
                messagebox.showwarning("تحذير", "لا توجد بيانات كافية لتدريب النموذج")
            
        except Exception as e:
            self.window.config(cursor="")
            messagebox.showerror("خطأ", f"خطأ في تدريب النموذج: {str(e)}")
    
    def custom_prediction(self):
        """تنبؤ مخصص"""
        messagebox.showinfo("معلومات", "ميزة التنبؤ المخصص قيد التطوير")
    
    def export_insights(self):
        """تصدير تقرير الرؤى"""
        messagebox.showinfo("معلومات", "ميزة تصدير تقرير الرؤى قيد التطوير")
    
    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        self.window.destroy()

# دالة لفتح نافذة رؤى الذكاء الصناعي
def open_ai_insights(parent=None):
    """فتح نافذة رؤى الذكاء الصناعي"""
    return AIInsightsWindow(parent)
