#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الإنذارات
يدير إنذارات الطلاب حسب أيام الغياب
"""

import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.database.database_manager import DatabaseManager

class WarningsManager:
    """مدير الإنذارات للطلاب"""
    
    def __init__(self):
        """تهيئة مدير الإنذارات"""
        self.db_manager = DatabaseManager()
        self.warning_levels = Config.WARNING_LEVELS
        self.create_warnings_table()
    
    def create_warnings_table(self):
        """إنشاء جدول الإنذارات"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS warnings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        student_id INTEGER NOT NULL,
                        warning_level TEXT NOT NULL,
                        absence_days INTEGER NOT NULL,
                        warning_date DATE NOT NULL,
                        semester INTEGER NOT NULL,
                        academic_year TEXT NOT NULL,
                        is_resolved BOOLEAN DEFAULT FALSE,
                        resolution_date DATE,
                        resolution_notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (student_id) REFERENCES students (id)
                    )
                ''')
                
                # إنشاء فهرس للبحث السريع
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_warnings_student_date 
                    ON warnings (student_id, warning_date)
                ''')
                
                conn.commit()
                
        except Exception as e:
            print(f"خطأ في إنشاء جدول الإنذارات: {e}")
    
    def calculate_absence_days(self, student_id: int, days_back: int = 30) -> int:
        """حساب أيام الغياب للطالب في فترة معينة"""
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days_back)
            
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT COUNT(*) FROM attendance 
                    WHERE student_id = ? 
                    AND date BETWEEN ? AND ?
                    AND absence_type IN ('unexcused', 'excused')
                ''', (student_id, start_date, end_date))
                
                result = cursor.fetchone()
                return result[0] if result else 0
                
        except Exception as e:
            print(f"خطأ في حساب أيام الغياب: {e}")
            return 0
    
    def get_warning_level(self, absence_days: int) -> Dict[str, Any]:
        """تحديد مستوى الإنذار حسب أيام الغياب"""
        warning_level = None
        
        for level, config in sorted(self.warning_levels.items(), 
                                  key=lambda x: x[1]['days'], reverse=True):
            if absence_days >= config['days']:
                warning_level = {
                    'level': level,
                    'name': config['name'],
                    'days': config['days'],
                    'color': config['color'],
                    'action': config['action']
                }
                break
        
        return warning_level
    
    def check_student_warnings(self, student_id: int) -> Dict[str, Any]:
        """فحص إنذارات طالب معين"""
        try:
            # حساب أيام الغياب
            absence_days = self.calculate_absence_days(student_id)
            
            # تحديد مستوى الإنذار
            warning_level = self.get_warning_level(absence_days)
            
            # الحصول على معلومات الطالب
            student_info = self.db_manager.get_student_by_id(student_id)
            
            result = {
                'student_id': student_id,
                'student_name': student_info.get('name', 'غير معروف') if student_info else 'غير معروف',
                'student_grade': student_info.get('grade', 'غير محدد') if student_info else 'غير محدد',
                'absence_days': absence_days,
                'warning_level': warning_level,
                'needs_warning': warning_level is not None,
                'last_check': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            print(f"خطأ في فحص إنذارات الطالب: {e}")
            return {
                'student_id': student_id,
                'error': str(e),
                'needs_warning': False
            }
    
    def check_all_students_warnings(self) -> List[Dict[str, Any]]:
        """فحص إنذارات جميع الطلاب"""
        try:
            students = self.db_manager.get_students()
            warnings_list = []
            
            for student in students:
                student_warning = self.check_student_warnings(student['id'])
                if student_warning.get('needs_warning', False):
                    warnings_list.append(student_warning)
            
            # ترتيب حسب شدة الإنذار
            warnings_list.sort(key=lambda x: x.get('absence_days', 0), reverse=True)
            
            return warnings_list
            
        except Exception as e:
            print(f"خطأ في فحص إنذارات جميع الطلاب: {e}")
            return []
    
    def create_warning_record(self, student_id: int, warning_level: str, absence_days: int) -> bool:
        """إنشاء سجل إنذار جديد"""
        try:
            current_date = datetime.now().date()
            current_semester = Config.get_current_semester()
            academic_year = Config.get_academic_year()
            
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                # التحقق من وجود إنذار مماثل في نفس الأسبوع
                week_ago = current_date - timedelta(days=7)
                cursor.execute('''
                    SELECT id FROM warnings 
                    WHERE student_id = ? 
                    AND warning_level = ?
                    AND warning_date >= ?
                    AND is_resolved = FALSE
                ''', (student_id, warning_level, week_ago))
                
                existing_warning = cursor.fetchone()
                
                if not existing_warning:
                    # إنشاء إنذار جديد
                    cursor.execute('''
                        INSERT INTO warnings 
                        (student_id, warning_level, absence_days, warning_date, 
                         semester, academic_year)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (student_id, warning_level, absence_days, current_date,
                          current_semester, academic_year))
                    
                    conn.commit()
                    return True
                
                return False  # إنذار موجود بالفعل
                
        except Exception as e:
            print(f"خطأ في إنشاء سجل الإنذار: {e}")
            return False
    
    def resolve_warning(self, warning_id: int, resolution_notes: str = "") -> bool:
        """حل إنذار معين"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE warnings 
                    SET is_resolved = TRUE,
                        resolution_date = ?,
                        resolution_notes = ?
                    WHERE id = ?
                ''', (datetime.now().date(), resolution_notes, warning_id))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            print(f"خطأ في حل الإنذار: {e}")
            return False
    
    def get_student_warning_history(self, student_id: int) -> List[Dict[str, Any]]:
        """الحصول على تاريخ إنذارات طالب معين"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT w.*, s.name as student_name
                    FROM warnings w
                    JOIN students s ON w.student_id = s.id
                    WHERE w.student_id = ?
                    ORDER BY w.warning_date DESC
                ''', (student_id,))
                
                warnings = []
                for row in cursor.fetchall():
                    warning = {
                        'id': row[0],
                        'student_id': row[1],
                        'warning_level': row[2],
                        'absence_days': row[3],
                        'warning_date': row[4],
                        'semester': row[5],
                        'academic_year': row[6],
                        'is_resolved': bool(row[7]),
                        'resolution_date': row[8],
                        'resolution_notes': row[9],
                        'created_at': row[10],
                        'student_name': row[11]
                    }
                    warnings.append(warning)
                
                return warnings
                
        except Exception as e:
            print(f"خطأ في الحصول على تاريخ الإنذارات: {e}")
            return []
    
    def get_warnings_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الإنذارات"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                # إحصائيات عامة
                cursor.execute('SELECT COUNT(*) FROM warnings WHERE is_resolved = FALSE')
                active_warnings = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM warnings WHERE is_resolved = TRUE')
                resolved_warnings = cursor.fetchone()[0]
                
                # إحصائيات حسب المستوى
                level_stats = {}
                for level in self.warning_levels.keys():
                    cursor.execute('''
                        SELECT COUNT(*) FROM warnings 
                        WHERE warning_level = ? AND is_resolved = FALSE
                    ''', (level,))
                    level_stats[level] = cursor.fetchone()[0]
                
                # الطلاب الأكثر تغيباً
                cursor.execute('''
                    SELECT s.name, COUNT(w.id) as warning_count
                    FROM warnings w
                    JOIN students s ON w.student_id = s.id
                    WHERE w.is_resolved = FALSE
                    GROUP BY w.student_id, s.name
                    ORDER BY warning_count DESC
                    LIMIT 10
                ''')
                
                top_students = [{'name': row[0], 'warnings': row[1]} 
                              for row in cursor.fetchall()]
                
                return {
                    'active_warnings': active_warnings,
                    'resolved_warnings': resolved_warnings,
                    'total_warnings': active_warnings + resolved_warnings,
                    'level_statistics': level_stats,
                    'top_absent_students': top_students,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات الإنذارات: {e}")
            return {}
    
    def auto_check_and_create_warnings(self) -> Dict[str, Any]:
        """فحص تلقائي وإنشاء إنذارات جديدة"""
        try:
            all_warnings = self.check_all_students_warnings()
            new_warnings_count = 0
            
            for warning_data in all_warnings:
                if warning_data.get('warning_level'):
                    level = warning_data['warning_level']['level']
                    student_id = warning_data['student_id']
                    absence_days = warning_data['absence_days']
                    
                    if self.create_warning_record(student_id, level, absence_days):
                        new_warnings_count += 1
            
            return {
                'total_checked': len(all_warnings),
                'new_warnings': new_warnings_count,
                'check_date': datetime.now().isoformat(),
                'warnings_list': all_warnings
            }
            
        except Exception as e:
            print(f"خطأ في الفحص التلقائي: {e}")
            return {'error': str(e)}
