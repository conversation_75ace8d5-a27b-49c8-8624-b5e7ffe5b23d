#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات التصدير والطباعة
تحتوي على دوال تصدير التقارير بصيغ مختلفة
"""

import os
from datetime import datetime
from typing import List, Dict, Any
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
    print("✅ reportlab متوفر ومثبت بنجاح")
except ImportError as e:
    REPORTLAB_AVAILABLE = False
    print(f"❌ reportlab غير متوفر: {str(e)}")

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

# محاولة استيراد مكتبات دعم العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT_AVAILABLE = True
    print("✅ دعم اللغة العربية متوفر")
except ImportError as e:
    ARABIC_SUPPORT_AVAILABLE = False
    print(f"⚠️ مكتبات دعم العربية غير متوفرة: {str(e)}")
    print("💡 لتفعيل دعم العربية، قم بتثبيت: pip install arabic-reshaper python-bidi")

class ExportUtils:
    """فئة أدوات التصدير"""
    
    def __init__(self):
        """تهيئة أدوات التصدير"""
        Config.ensure_directories()
    
    def export_to_pdf(self, data: List[List[Any]], headers: List[str],
                     title: str, filename: str) -> bool:
        """تصدير البيانات إلى ملف PDF مع دعم كامل للغة العربية"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة reportlab غير متوفرة. يرجى تثبيتها باستخدام: pip install reportlab")

        try:
            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            story = []

            # محاولة تسجيل خط عربي
            arabic_font_name = self._register_arabic_font()

            # إعداد الأنماط
            styles = getSampleStyleSheet()

            # نمط العنوان
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=arabic_font_name,
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # توسيط
                textColor=colors.darkblue
            )

            # تحميل إعدادات المدرسة
            settings = self._load_settings()

            # إضافة رأس المدرسة
            school_header_style = ParagraphStyle(
                'SchoolHeader',
                parent=styles['Normal'],
                fontName=arabic_font_name,
                fontSize=12,
                spaceAfter=6,
                alignment=1,  # توسيط
                textColor=colors.darkblue
            )

            school_name = settings.get('school_name', Config.SCHOOL_NAME)
            school_address = settings.get('school_address', Config.SCHOOL_ADDRESS)

            story.append(Paragraph(self._process_arabic_text(school_name), school_header_style))
            story.append(Paragraph(self._process_arabic_text(school_address), school_header_style))
            story.append(Spacer(1, 12))

            # إضافة العنوان مع دعم العربية
            arabic_title = self._process_arabic_text(title)
            story.append(Paragraph(arabic_title, title_style))
            story.append(Spacer(1, 12))

            # إضافة معلومات التقرير
            info_style = ParagraphStyle(
                'Info',
                parent=styles['Normal'],
                fontName=arabic_font_name,
                fontSize=10,
                spaceAfter=6,
                alignment=2  # محاذاة يمين للعربية
            )

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            academic_year = Config.get_academic_year()
            semester = Config.get_current_semester()
            semester_name = Config.SEMESTERS.get(semester, "غير محدد")

            info_text1 = self._process_arabic_text(f"السنة الدراسية: {academic_year}")
            info_text2 = self._process_arabic_text(f"الفصل الدراسي: {semester_name}")
            info_text3 = self._process_arabic_text(f"تاريخ الإنشاء: {current_time}")
            info_text4 = self._process_arabic_text(f"عدد السجلات: {len(data)}")

            story.append(Paragraph(info_text1, info_style))
            story.append(Paragraph(info_text2, info_style))
            story.append(Paragraph(info_text3, info_style))
            story.append(Paragraph(info_text4, info_style))
            story.append(Spacer(1, 12))

            # إنشاء الجدول
            if data:
                # معالجة الرؤوس العربية
                arabic_headers = [self._process_arabic_text(str(h)) for h in headers]

                # معالجة البيانات العربية
                arabic_data = []
                for row in data:
                    arabic_row = [self._process_arabic_text(str(cell)) for cell in row]
                    arabic_data.append(arabic_row)

                # إضافة الرؤوس
                table_data = [arabic_headers] + arabic_data

                # إنشاء الجدول
                table = Table(table_data)

                # تنسيق الجدول
                table.setStyle(TableStyle([
                    # تنسيق الرؤوس
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), arabic_font_name),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                    # تنسيق البيانات
                    ('FONTNAME', (0, 1), (-1, -1), arabic_font_name),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

                    # تلوين الصفوف بالتناوب
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
                ]))

                story.append(table)
            else:
                no_data_text = self._process_arabic_text("لا توجد بيانات للعرض")
                story.append(Paragraph(no_data_text, styles['Normal']))

            # إضافة مساحة قبل التوقيع
            story.append(Spacer(1, 30))

            # إضافة توقيع المدير والختم
            self._add_signature_and_stamp(story, arabic_font_name, styles)

            # إنشاء المستند
            doc.build(story)
            return True

        except Exception as e:
            raise Exception(f"خطأ في تصدير PDF: {str(e)}")

    def _add_signature_and_stamp(self, story, arabic_font_name: str, styles):
        """إضافة توقيع المدير والختم إلى التقرير"""
        try:
            # تحميل الإعدادات المحفوظة
            settings = self._load_settings()

            # التحقق من إعدادات العرض
            show_signature = settings.get('show_signature', True)
            show_stamp = settings.get('show_stamp', True)

            if not show_signature and not show_stamp:
                return  # لا تعرض شيئاً إذا كانت الإعدادات معطلة

            # الحصول على النصوص من الإعدادات أو القيم الافتراضية
            principal_name = settings.get('principal_name', Config.PRINCIPAL_NAME)
            principal_title = settings.get('principal_title', Config.PRINCIPAL_TITLE)
            signature_text = settings.get('signature_text', Config.PRINCIPAL_SIGNATURE)
            stamp_text = settings.get('stamp_text', Config.STAMP_PLACEHOLDER)

            # إنشاء جدول للتوقيع والختم
            signature_data = []

            if show_stamp and show_signature:
                signature_data = [
                    [
                        self._process_arabic_text(stamp_text) if show_stamp else "",
                        self._process_arabic_text(principal_name) if show_signature else ""
                    ],
                    [
                        "",
                        self._process_arabic_text(principal_title) if show_signature else ""
                    ],
                    [
                        "",
                        self._process_arabic_text(signature_text) if show_signature else ""
                    ]
                ]
            elif show_signature:
                signature_data = [
                    [self._process_arabic_text(principal_name)],
                    [self._process_arabic_text(principal_title)],
                    [self._process_arabic_text(signature_text)]
                ]
            elif show_stamp:
                signature_data = [
                    [self._process_arabic_text(stamp_text)]
                ]

            # إنشاء جدول التوقيع
            signature_table = Table(signature_data, colWidths=[2*inch, 3*inch])

            # تنسيق جدول التوقيع
            signature_table.setStyle(TableStyle([
                # إزالة الحدود
                ('GRID', (0, 0), (-1, -1), 0, colors.white),

                # محاذاة النص
                ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # الختم في الوسط
                ('ALIGN', (1, 0), (1, -1), 'CENTER'),  # التوقيع في الوسط

                # الخط والحجم
                ('FONTNAME', (0, 0), (-1, -1), arabic_font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 10),

                # المحاذاة العمودية
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),

                # تنسيق خاص للختم
                ('FONTSIZE', (0, 0), (0, 0), 12),
                ('TEXTCOLOR', (0, 0), (0, 0), colors.blue),

                # تنسيق خاص لاسم المدير
                ('FONTSIZE', (1, 0), (1, 0), 12),
                ('TEXTCOLOR', (1, 0), (1, 0), colors.black),

                # مساحة بين الصفوف
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(signature_table)

            # إضافة تاريخ التوقيع
            date_style = ParagraphStyle(
                'DateStyle',
                parent=styles['Normal'],
                fontName=arabic_font_name,
                fontSize=10,
                alignment=2,  # محاذاة يمين
                spaceAfter=12
            )

            current_date = datetime.now().strftime("%Y/%m/%d")
            date_text = self._process_arabic_text(f"التاريخ: {current_date}")
            story.append(Spacer(1, 12))
            story.append(Paragraph(date_text, date_style))

        except Exception as e:
            # في حالة حدوث خطأ، أضف توقيع بسيط
            simple_signature = self._process_arabic_text(f"{Config.PRINCIPAL_NAME}\n{Config.PRINCIPAL_TITLE}")
            story.append(Paragraph(simple_signature, styles['Normal']))

    def _register_arabic_font(self) -> str:
        """تسجيل خط عربي للاستخدام في PDF"""
        try:
            import platform

            if platform.system() == "Windows":
                # محاولة استخدام خطوط Windows العربية
                font_paths = [
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/calibri.ttf"
                ]

                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Arabic', font_path))
                            return 'Arabic'
                        except:
                            continue

            # إذا فشل تحميل الخط العربي، استخدم Helvetica
            return 'Helvetica'

        except Exception:
            return 'Helvetica'

    def _process_arabic_text(self, text: str) -> str:
        """معالجة النص العربي لعرض صحيح في PDF"""
        if not text:
            return ""

        try:
            if ARABIC_SUPPORT_AVAILABLE:
                # إعادة تشكيل النص العربي
                reshaped_text = arabic_reshaper.reshape(text)
                # تطبيق خوارزمية BiDi للعرض الصحيح
                bidi_text = get_display(reshaped_text)
                return bidi_text
            else:
                # إذا لم تكن مكتبات العربية متوفرة، أرجع النص كما هو
                return text
        except Exception:
            # في حالة حدوث خطأ، أرجع النص كما هو
            return text

    def _load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        settings_file = os.path.join(Config.DATA_DIR, "settings.json")
        default_settings = {}

        try:
            if os.path.exists(settings_file):
                import json
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    default_settings.update(saved_settings)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

        return default_settings


    
    def export_to_excel(self, data: List[List[Any]], headers: List[str], 
                       title: str, filename: str) -> bool:
        """تصدير البيانات إلى ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            raise ImportError("مكتبة openpyxl غير متوفرة. يرجى تثبيتها باستخدام: pip install openpyxl")
        
        try:
            # إنشاء مصنف جديد
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "تقرير الغياب"
            
            # إعداد الأنماط
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            data_alignment = Alignment(horizontal="center", vertical="center")
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # إضافة العنوان
            worksheet.merge_cells('A1:' + get_column_letter(len(headers)) + '1')
            title_cell = worksheet['A1']
            title_cell.value = title
            title_cell.font = Font(bold=True, size=16)
            title_cell.alignment = Alignment(horizontal="center", vertical="center")
            
            # إضافة معلومات التقرير
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            worksheet.merge_cells('A2:' + get_column_letter(len(headers)) + '2')
            info_cell = worksheet['A2']
            info_cell.value = f"تاريخ الإنشاء: {current_time} | عدد السجلات: {len(data)}"
            info_cell.alignment = Alignment(horizontal="center")
            
            # إضافة الرؤوس
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=4, column=col)
                cell.value = header
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border
            
            # إضافة البيانات
            for row_idx, row_data in enumerate(data, 5):
                for col_idx, value in enumerate(row_data, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.value = value
                    cell.alignment = data_alignment
                    cell.border = border
                    
                    # تلوين الصفوف بالتناوب
                    if row_idx % 2 == 0:
                        cell.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
            
            # تعديل عرض الأعمدة
            for col in range(1, len(headers) + 1):
                column_letter = get_column_letter(col)
                worksheet.column_dimensions[column_letter].width = 15
            
            # حفظ الملف
            workbook.save(filename)
            return True
            
        except Exception as e:
            raise Exception(f"خطأ في تصدير Excel: {str(e)}")
    
    def export_to_csv(self, data: List[List[Any]], headers: List[str], filename: str) -> bool:
        """تصدير البيانات إلى ملف CSV"""
        try:
            import csv
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # كتابة الرؤوس
                writer.writerow(headers)
                
                # كتابة البيانات
                for row in data:
                    writer.writerow(row)
            
            return True
            
        except Exception as e:
            raise Exception(f"خطأ في تصدير CSV: {str(e)}")
    
    def print_report(self, data: List[List[Any]], headers: List[str], title: str) -> bool:
        """طباعة التقرير"""
        try:
            # إنشاء ملف PDF مؤقت للطباعة
            temp_filename = os.path.join(Config.REPORTS_DIR, f"temp_print_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")

            # تصدير إلى PDF
            success = self.export_to_pdf(data, headers, title, temp_filename)

            if success:
                # فتح ملف PDF للطباعة (يعتمد على نظام التشغيل)
                import subprocess
                import platform

                system = platform.system()
                try:
                    if system == "Windows":
                        # محاولة فتح الملف للطباعة
                        try:
                            os.startfile(temp_filename, "print")
                        except OSError:
                            # إذا فشل، فتح الملف عادي ليتمكن المستخدم من طباعته يدوياً
                            os.startfile(temp_filename)
                            raise Exception("تم فتح الملف. يرجى طباعته يدوياً من برنامج عرض PDF")
                    elif system == "Darwin":  # macOS
                        subprocess.run(["lpr", temp_filename])
                    else:  # Linux
                        subprocess.run(["lp", temp_filename])
                except Exception as print_error:
                    # إذا فشلت الطباعة، فتح الملف للعرض
                    try:
                        if system == "Windows":
                            os.startfile(temp_filename)
                        elif system == "Darwin":
                            subprocess.run(["open", temp_filename])
                        else:
                            subprocess.run(["xdg-open", temp_filename])
                        raise Exception(f"تم إنشاء الملف وفتحه للعرض. يرجى طباعته يدوياً.\nمسار الملف: {temp_filename}")
                    except:
                        raise Exception(f"تم إنشاء الملف بنجاح في: {temp_filename}\nيرجى فتحه وطباعته يدوياً")

                return True

            return False

        except Exception as e:
            raise Exception(f"خطأ في الطباعة: {str(e)}")
    
    def get_export_filename(self, base_name: str, extension: str) -> str:
        """إنشاء اسم ملف للتصدير"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{base_name}_{timestamp}.{extension}"
        return os.path.join(Config.EXPORTS_DIR, filename)
    
    def is_pdf_available(self) -> bool:
        """التحقق من توفر إمكانية تصدير PDF"""
        # التحقق الديناميكي من reportlab
        try:
            import reportlab
            return True
        except ImportError:
            return False
    
    def is_excel_available(self) -> bool:
        """التحقق من توفر إمكانية تصدير Excel"""
        return OPENPYXL_AVAILABLE

# إنشاء مثيل عام للاستخدام
export_utils = ExportUtils()
