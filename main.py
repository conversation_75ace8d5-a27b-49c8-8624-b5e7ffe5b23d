#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج تسجيل غياب الطلاب الذكي
نظام شامل لإدارة غياب طلاب المرحلة المتوسطة

المطور: مساعد الذكاء الصناعي
التاريخ: 2025-08-01
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مجلد المشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.main_window import MainWindow
from src.database.database_manager import DatabaseManager
from src.utils.config import Config

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        os.makedirs('data', exist_ok=True)
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        app = MainWindow(root)
        
        # تشغيل البرنامج
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
