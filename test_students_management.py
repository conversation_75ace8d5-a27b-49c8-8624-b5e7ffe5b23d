#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة إدارة الطلاب
اختبار شامل لجميع الأيقونات والوظائف في صفحة إدارة الطلاب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_students_management_icons():
    """اختبار أيقونات صفحة إدارة الطلاب"""
    print("🔍 اختبار أيقونات صفحة إدارة الطلاب...")
    
    try:
        from src.gui.students_management import StudentsManagementWindow
        from src.utils.themes import theme_manager
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار صفحة إدارة الطلاب")
        root.geometry("500x400")
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🧪 اختبار صفحة إدارة الطلاب", 
                               font=("Tahoma", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # معلومات الاختبار
        info_text = """
        هذا اختبار لصفحة إدارة الطلاب المحسنة.
        
        الأيقونات والوظائف المتاحة:
        
        شريط الأدوات:
        ➕ إضافة طالب جديد
        🔄 تحديث القائمة
        
        منطقة البحث:
        🔍 بحث في الطلاب
        🗑️ مسح البحث
        
        العمليات الأساسية:
        ➕ إضافة طالب
        ✏️ تحديث البيانات
        🗑️ حذف الطالب
        🧹 مسح الحقول
        🔄 تحديث القائمة
        
        العمليات المتقدمة:
        📊 إحصائيات الطالب
        📄 تصدير البيانات
        📥 استيراد البيانات
        🖨️ طباعة التفاصيل
        ❌ إغلاق النافذة
        
        انقر على الزر أدناه لفتح صفحة إدارة الطلاب.
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.CENTER)
        info_label.pack(pady=(0, 20))
        
        # زر فتح صفحة إدارة الطلاب
        def open_students_management():
            try:
                students_window = StudentsManagementWindow(root)
                print("✅ تم فتح صفحة إدارة الطلاب بنجاح")
                print("📝 تحقق من:")
                print("   - عرض جميع الأيقونات بشكل صحيح")
                print("   - عمل جميع الأزرار")
                print("   - البحث والتصفية")
                print("   - إضافة وتحديث وحذف الطلاب")
                print("   - دعم RTL كامل")
            except Exception as e:
                print(f"❌ خطأ في فتح صفحة إدارة الطلاب: {str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("خطأ", f"خطأ في فتح صفحة إدارة الطلاب: {str(e)}")
        
        students_btn = ttk.Button(main_frame, text="👥 فتح صفحة إدارة الطلاب", 
                                 command=open_students_management)
        students_btn.pack(pady=10)
        
        # زر اختبار الثيمات
        def test_themes():
            themes = ["default", "dark", "blue", "green"]
            current_index = themes.index(theme_manager.current_theme)
            next_theme = themes[(current_index + 1) % len(themes)]
            
            theme_manager.set_theme(next_theme)
            theme_manager.apply_theme_to_style(style)
            
            theme_name = theme_manager.get_current_theme()['name']
            print(f"🎨 تم تغيير الثيم إلى: {theme_name}")
        
        theme_btn = ttk.Button(main_frame, text="🎨 تبديل الثيم", 
                              command=test_themes)
        theme_btn.pack(pady=5)
        
        # معلومات إضافية
        footer_label = ttk.Label(main_frame, 
                                text="تحقق من جميع الأيقونات والوظائف في صفحة إدارة الطلاب", 
                                font=("Tahoma", 10), foreground="gray")
        footer_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
        print("✅ تم إنشاء نافذة اختبار صفحة إدارة الطلاب بنجاح")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة إدارة الطلاب: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_icons_display():
    """اختبار عرض الأيقونات"""
    print("\n🎨 اختبار عرض الأيقونات...")
    
    try:
        # قائمة الأيقونات المستخدمة
        icons = [
            "➕", "✏️", "🗑️", "🧹", "🔄", "📊", "📄", "📥", "🖨️", "❌",
            "🔍", "👥", "📋", "⚡", "🏠", "🎨", "⚙️", "📝", "📅"
        ]
        
        print("الأيقونات المستخدمة في صفحة إدارة الطلاب:")
        for i, icon in enumerate(icons, 1):
            print(f"  {i:2d}. {icon}")
        
        print(f"\nإجمالي الأيقونات: {len(icons)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأيقونات: {str(e)}")
        return False

def test_button_functions():
    """اختبار وظائف الأزرار"""
    print("\n🔧 اختبار وظائف الأزرار...")
    
    try:
        from src.gui.students_management import StudentsManagementWindow
        
        # قائمة الوظائف المطلوبة
        required_functions = [
            'add_student',
            'update_student', 
            'delete_student',
            'clear_fields',
            'load_students',
            'search_students',
            'clear_search',
            'show_student_stats',
            'export_students',
            'import_students',
            'print_student_info',
            'on_closing'
        ]
        
        print("فحص وجود الوظائف المطلوبة:")
        for func_name in required_functions:
            if hasattr(StudentsManagementWindow, func_name):
                print(f"  ✅ {func_name}")
            else:
                print(f"  ❌ {func_name} - مفقود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {str(e)}")
        return False

def test_components_integration():
    """اختبار تكامل المكونات"""
    print("\n🔗 اختبار تكامل المكونات...")
    
    try:
        # اختبار استيراد المكونات
        from src.gui.components import ModernCard, ModernEntry, ModernCombobox, ModernTreeview, ModernButton
        from src.utils.rtl_support import rtl_support
        from src.utils.themes import theme_manager
        
        print("✅ جميع المكونات المطلوبة متوفرة")
        
        # اختبار إنشاء المكونات
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        test_frame = ttk.Frame(root)
        
        # اختبار المكونات
        card = ModernCard(test_frame, title="اختبار")
        entry = ModernEntry(test_frame, "اختبار", placeholder="نص تجريبي")
        combo = ModernCombobox(test_frame, "اختبار", values=["خيار 1", "خيار 2"])
        table = ModernTreeview(test_frame, columns=['عمود 1', 'عمود 2'])
        button = ModernButton(test_frame, text="🔍 اختبار", command=lambda: None)
        
        print("✅ إنشاء جميع المكونات يعمل بشكل صحيح")
        
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لاختبار صفحة إدارة الطلاب"""
    print("🚀 بدء اختبار صفحة إدارة الطلاب...")
    print("=" * 60)
    
    # اختبار عرض الأيقونات
    icons_ok = test_icons_display()
    
    # اختبار وظائف الأزرار
    functions_ok = test_button_functions()
    
    # اختبار تكامل المكونات
    integration_ok = test_components_integration()
    
    if icons_ok and functions_ok and integration_ok:
        print("\n🖥️ سيتم فتح نافذة اختبار صفحة إدارة الطلاب...")
        print("تحقق من الميزات التالية:")
        print("1. عرض جميع الأيقونات بشكل صحيح")
        print("2. عمل جميع الأزرار والوظائف")
        print("3. البحث والتصفية المتقدمة")
        print("4. إضافة وتحديث وحذف الطلاب")
        print("5. الجدول التفاعلي مع قائمة السياق")
        print("6. دعم RTL كامل")
        print("7. التصميم المحسن والثيمات")
        print("\nأغلق النافذة للمتابعة...")
        
        # اختبار الصفحة
        page_ok = test_students_management_icons()
        
        if page_ok:
            print("\n🎉 اختبار صفحة إدارة الطلاب مكتمل!")
        else:
            print("\n⚠️ فشل في اختبار صفحة إدارة الطلاب")
    else:
        print("\n⚠️ مشكلة في المتطلبات الأساسية")

if __name__ == "__main__":
    main()
