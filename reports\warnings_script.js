// ملف JavaScript الخاص بصفحة إنذارات الغياب

// بيانات الطلاب والإنذارات (يمكن استبدالها ببيانات من قاعدة البيانات)
const studentsData = [
    {
        id: '2024001',
        name: 'أحم<PERSON> محمد العلي',
        grade: 'الثاني المتوسط',
        section: 'أ',
        absenceDays: 8,
        warningType: 'first',
        warningDate: '1445/03/10',
        notes: 'غياب متكرر بدون عذر',
        parentPhone: '0501234567'
    },
    {
        id: '2024002',
        name: 'خالد عبدالله السالم',
        grade: 'الأول المتوسط',
        section: 'ب',
        absenceDays: 15,
        warningType: 'second',
        warningDate: '1445/03/12',
        notes: 'تم إبلاغ ولي الأمر هاتفياً',
        parentPhone: '0507654321'
    },
    {
        id: '2024003',
        name: 'محمد سعد الأحمد',
        grade: 'الثالث المتوسط',
        section: 'أ',
        absenceDays: 22,
        warningType: 'final',
        warningDate: '1445/03/14',
        notes: 'مطلوب حضور ولي الأمر فوراً',
        parentPhone: '0551234567'
    },
    {
        id: '2024004',
        name: 'عبدالرحمن أحمد الزهراني',
        grade: 'الثاني المتوسط',
        section: 'ج',
        absenceDays: 6,
        warningType: 'first',
        warningDate: '1445/03/15',
        notes: 'تأخير متكرر في الحضور',
        parentPhone: '0559876543'
    },
    {
        id: '2024005',
        name: 'يوسف علي المطيري',
        grade: 'الأول المتوسط',
        section: 'أ',
        absenceDays: 12,
        warningType: 'second',
        warningDate: '1445/03/13',
        notes: 'غياب بدون إذن مسبق',
        parentPhone: '0503456789'
    }
];

// متغيرات عامة
let filteredData = [...studentsData];
let currentSort = { field: null, direction: 'asc' };

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    renderWarnings();
    updateSummary();
    setupEventListeners();
});

// تهيئة الصفحة
function initializePage() {
    // تحديث التاريخ الحالي
    updateCurrentDate();
    
    // إضافة تأثيرات الحركة
    addAnimations();
    
    // تحديث الإحصائيات كل دقيقة
    setInterval(updateSummary, 60000);
}

// تحديث التاريخ الحالي
function updateCurrentDate() {
    const now = new Date();
    const hijriYear = 1445;
    const hijriMonth = String(now.getMonth() + 1).padStart(2, '0');
    const hijriDay = String(now.getDate()).padStart(2, '0');
    const hijriDate = `${hijriYear}/${hijriMonth}/${hijriDay}`;
    
    // إضافة التاريخ إلى الرأس إذا لم يكن موجوداً
    const subtitle = document.querySelector('.header .subtitle');
    if (subtitle && !subtitle.textContent.includes('التاريخ')) {
        subtitle.innerHTML += `<br>التاريخ: ${hijriDate}`;
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث المباشر
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterWarnings, 300));
    }
    
    // فلاتر التصفية
    const gradeFilter = document.getElementById('gradeFilter');
    const warningFilter = document.getElementById('warningFilter');
    
    if (gradeFilter) gradeFilter.addEventListener('change', filterWarnings);
    if (warningFilter) warningFilter.addEventListener('change', filterWarnings);
    
    // اختصارات لوحة المفاتيح
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// معالجة اختصارات لوحة المفاتيح
function handleKeyboardShortcuts(event) {
    // Ctrl + P للطباعة
    if (event.ctrlKey && event.key === 'p') {
        event.preventDefault();
        exportReport();
    }
    
    // Ctrl + F للبحث
    if (event.ctrlKey && event.key === 'f') {
        event.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.focus();
    }
}

// تصفية الإنذارات
function filterWarnings() {
    const grade = document.getElementById('gradeFilter')?.value || '';
    const warning = document.getElementById('warningFilter')?.value || '';
    const search = document.getElementById('searchInput')?.value.toLowerCase() || '';
    
    filteredData = studentsData.filter(student => {
        const matchesGrade = !grade || student.grade === grade;
        const matchesWarning = !warning || student.warningType === warning;
        const matchesSearch = !search || 
            student.name.toLowerCase().includes(search) ||
            student.id.includes(search);
        
        return matchesGrade && matchesWarning && matchesSearch;
    });
    
    renderWarnings();
    updateSummary();
}

// عرض الإنذارات
function renderWarnings() {
    const container = document.getElementById('warningsContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (filteredData.length === 0) {
        container.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #6c757d;">
                <h3>لا توجد إنذارات تطابق معايير البحث</h3>
                <p>جرب تغيير فلاتر البحث أو مسح النص المدخل</p>
            </div>
        `;
        return;
    }
    
    filteredData.forEach((student, index) => {
        const card = createWarningCard(student);
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
        container.appendChild(card);
    });
}

// إنشاء بطاقة إنذار
function createWarningCard(student) {
    const card = document.createElement('div');
    card.className = `warning-card ${student.warningType}`;
    
    const warningTypeText = {
        'first': 'إنذار أول',
        'second': 'إنذار ثاني',
        'final': 'إنذار نهائي'
    };
    
    card.innerHTML = `
        <div class="warning-header">
            <span class="warning-type ${student.warningType}">${warningTypeText[student.warningType]}</span>
            <span style="color: #6c757d; font-size: 12px;">${student.warningDate}</span>
        </div>
        <div class="student-info">
            <h3>${student.name}</h3>
            <div class="student-details">
                رقم الطالب: ${student.id}<br>
                الصف: ${student.grade} - شعبة ${student.section}<br>
                هاتف ولي الأمر: ${student.parentPhone}
            </div>
        </div>
        <div class="absence-count">
            <div class="number">${student.absenceDays}</div>
            <div class="label">أيام غياب</div>
        </div>
        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 13px;">
            <strong>ملاحظات:</strong> ${student.notes}
        </div>
        <div class="actions">
            <button class="btn btn-warning btn-sm" onclick="sendNotification('${student.id}')">
                إرسال إشعار
            </button>
            <button class="btn btn-primary btn-sm" onclick="viewDetails('${student.id}')">
                تفاصيل
            </button>
            <button class="btn btn-success btn-sm" onclick="callParent('${student.parentPhone}')">
                اتصال
            </button>
        </div>
    `;
    
    return card;
}

// تحديث الملخص
function updateSummary() {
    const summary = {
        first: filteredData.filter(s => s.warningType === 'first').length,
        second: filteredData.filter(s => s.warningType === 'second').length,
        final: filteredData.filter(s => s.warningType === 'final').length,
        total: filteredData.length
    };
    
    // تحديث أرقام الملخص
    const summaryItems = document.querySelectorAll('.summary-number');
    if (summaryItems.length >= 4) {
        summaryItems[0].textContent = summary.first;
        summaryItems[1].textContent = summary.second;
        summaryItems[2].textContent = summary.final;
        summaryItems[3].textContent = summary.total;
    }
    
    // إضافة تأثير النبض للأرقام المحدثة
    summaryItems.forEach(item => {
        item.classList.add('pulse');
        setTimeout(() => item.classList.remove('pulse'), 1000);
    });
}

// إرسال إشعار
function sendNotification(studentId) {
    const student = studentsData.find(s => s.id === studentId);
    if (!student) return;
    
    // محاكاة إرسال الإشعار
    showAlert(`تم إرسال إشعار إلى ولي أمر الطالب: ${student.name}`, 'success');
    
    // هنا يمكن إضافة كود إرسال الإشعار الفعلي
    console.log('إرسال إشعار للطالب:', student);
}

// عرض تفاصيل الطالب
function viewDetails(studentId) {
    const student = studentsData.find(s => s.id === studentId);
    if (!student) return;
    
    // إنشاء نافذة منبثقة للتفاصيل
    const modal = createModal(`
        <h3>تفاصيل الطالب: ${student.name}</h3>
        <div style="text-align: right; line-height: 1.8;">
            <p><strong>رقم الطالب:</strong> ${student.id}</p>
            <p><strong>الصف:</strong> ${student.grade} - شعبة ${student.section}</p>
            <p><strong>عدد أيام الغياب:</strong> ${student.absenceDays}</p>
            <p><strong>نوع الإنذار:</strong> ${getWarningTypeText(student.warningType)}</p>
            <p><strong>تاريخ الإنذار:</strong> ${student.warningDate}</p>
            <p><strong>هاتف ولي الأمر:</strong> ${student.parentPhone}</p>
            <p><strong>ملاحظات:</strong> ${student.notes}</p>
        </div>
    `);
    
    document.body.appendChild(modal);
}

// الاتصال بولي الأمر
function callParent(phoneNumber) {
    showAlert(`سيتم الاتصال بالرقم: ${phoneNumber}`, 'info');
    
    // هنا يمكن إضافة كود الاتصال الفعلي أو فتح تطبيق الهاتف
    if (navigator.userAgent.match(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i)) {
        window.open(`tel:${phoneNumber}`);
    }
}

// تصدير التقرير
function exportReport() {
    // إخفاء الأزرار قبل الطباعة
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => btn.style.display = 'none');
    
    // طباعة الصفحة
    window.print();
    
    // إظهار الأزرار بعد الطباعة
    setTimeout(() => {
        buttons.forEach(btn => btn.style.display = '');
    }, 1000);
}

// دوال مساعدة
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function getWarningTypeText(type) {
    const types = {
        'first': 'إنذار أول',
        'second': 'إنذار ثاني',
        'final': 'إنذار نهائي'
    };
    return types[type] || type;
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 1000;
        font-weight: bold;
        animation: slideIn 0.3s ease;
    `;
    alertDiv.textContent = message;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => alertDiv.remove(), 300);
    }, 3000);
}

function createModal(content) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    `;
    
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 12px;
        max-width: 500px;
        width: 90%;
        max-height: 80%;
        overflow-y: auto;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    `;
    
    modalContent.innerHTML = content + `
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-primary" onclick="this.closest('.modal').remove()">إغلاق</button>
        </div>
    `;
    
    modal.className = 'modal';
    modal.appendChild(modalContent);
    
    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
    
    return modal;
}

function addAnimations() {
    // إضافة تأثيرات CSS للحركة
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// تصدير الدوال للاستخدام العام
window.filterWarnings = filterWarnings;
window.exportReport = exportReport;
window.sendNotification = sendNotification;
window.viewDetails = viewDetails;
window.callParent = callParent;
