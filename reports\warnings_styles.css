/* ملف الأنماط الخاص بصفحة إنذارات الغياب */

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>homa', 'Arial', sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    direction: rtl;
    min-height: 100vh;
}

/* تصميم الحاوية الرئيسية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* تصميم الرأس */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.1) 10px,
        rgba(255,255,255,0.1) 20px
    );
    animation: headerAnimation 20s linear infinite;
}

@keyframes headerAnimation {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.header h1 {
    margin: 0;
    font-size: 32px;
    position: relative;
    z-index: 1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    margin: 10px 0 0 0;
    font-size: 16px;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* قسم أدوات التحكم */
.controls-section {
    padding: 25px;
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 3px solid #dee2e6;
}

.controls-title {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 15px 20px;
    margin: -25px -25px 20px -25px;
    font-weight: bold;
    font-size: 18px;
    text-align: center;
    border-radius: 0;
}

.filter-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: bold;
    color: #495057;
    min-width: 100px;
}

.filter-group select,
.filter-group input {
    padding: 12px 15px;
    border: 2px solid #ced4da;
    border-radius: 8px;
    font-family: inherit;
    transition: all 0.3s ease;
    min-width: 150px;
    font-size: 14px;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    transform: translateY(-1px);
}

/* تصميم الأزرار */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* قسم الإنذارات */
.warnings-section {
    padding: 25px;
}

.warnings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* بطاقات الإنذارات */
.warning-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-right: 5px solid;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.warning-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent 0%, currentColor 100%);
}

.warning-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.warning-card.first { 
    border-right-color: #ffc107;
    color: #ffc107;
}

.warning-card.second { 
    border-right-color: #fd7e14;
    color: #fd7e14;
}

.warning-card.final { 
    border-right-color: #dc3545;
    color: #dc3545;
}

.warning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.warning-type {
    padding: 6px 12px;
    border-radius: 20px;
    color: white;
    font-weight: bold;
    font-size: 12px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.warning-type.first { background: #ffc107; }
.warning-type.second { background: #fd7e14; }
.warning-type.final { background: #dc3545; }

/* معلومات الطالب */
.student-info h3 {
    margin: 0 0 10px 0;
    color: #343a40;
    font-size: 18px;
    font-weight: bold;
}

.student-details {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.6;
}

.absence-count {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin: 15px 0;
    border: 1px solid #dee2e6;
}

.absence-count .number {
    font-size: 28px;
    font-weight: bold;
    color: #dc3545;
    display: block;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.absence-count .label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

/* قسم الملخص */
.summary-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 25px;
    margin: 20px 0;
    border-radius: 12px;
    border: 1px solid #90caf9;
}

.summary-section h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #1565c0;
    font-size: 20px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    text-align: center;
}

.summary-item {
    background: white;
    padding: 20px 15px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.summary-item:hover {
    transform: translateY(-3px);
}

.summary-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.summary-number.first { color: #ffc107; }
.summary-number.second { color: #fd7e14; }
.summary-number.final { color: #dc3545; }
.summary-number.total { color: #007bff; }

/* التصميم المتجاوب */
@media print {
    body { 
        background: white; 
        padding: 0;
    }
    .btn { display: none; }
    .container { 
        box-shadow: none; 
        max-width: none;
    }
    .header::before { display: none; }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .warnings-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .warning-card {
        padding: 15px;
    }
    
    .controls-section {
        padding: 15px;
    }
    
    .warnings-section {
        padding: 15px;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* تحسينات إضافية للطباعة */
@page {
    margin: 2cm;
    size: A4;
}

@media print {
    .warning-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    .summary-section {
        break-inside: avoid;
    }
}
