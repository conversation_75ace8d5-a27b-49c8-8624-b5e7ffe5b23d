<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم إنذارات الغياب - مدرسة ضباء المتوسطة للبنين</title>
    <link rel="stylesheet" href="warnings_styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏫</text></svg>">
</head>
<body>
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="header">
            <h1>🚨 لوحة تحكم إنذارات الغياب</h1>
            <div class="subtitle">
                مدرسة ضباء المتوسطة للبنين - منطقة تبوك<br>
                <span id="currentDateTime"></span>
            </div>
        </div>
        
        <!-- معلومات المدرسة -->
        <div style="background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%); padding: 15px; text-align: center; border-bottom: 2px solid #dee2e6;">
            <strong>📍 العنوان:</strong> ضباء، منطقة تبوك، المملكة العربية السعودية |
            <strong>📞 الهاتف:</strong> 014-1234567 |
            <strong>📧 البريد:</strong> <EMAIL>
        </div>
        
        <!-- قسم أدوات التحكم -->
        <div class="controls-section">
            <div class="controls-title">🔍 أدوات البحث والتصفية</div>
            
            <div class="filter-row">
                <div class="filter-group">
                    <label>📚 الصف:</label>
                    <select id="gradeFilter">
                        <option value="">جميع الصفوف</option>
                        <option value="الأول المتوسط">الأول المتوسط</option>
                        <option value="الثاني المتوسط">الثاني المتوسط</option>
                        <option value="الثالث المتوسط">الثالث المتوسط</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>⚠️ نوع الإنذار:</label>
                    <select id="warningFilter">
                        <option value="">جميع الإنذارات</option>
                        <option value="first">إنذار أول</option>
                        <option value="second">إنذار ثاني</option>
                        <option value="final">إنذار نهائي</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>🔎 البحث:</label>
                    <input type="text" id="searchInput" placeholder="اسم الطالب أو رقمه...">
                </div>
                
                <button class="btn btn-primary" onclick="filterWarnings()">
                    🔍 بحث
                </button>
                <button class="btn btn-success" onclick="exportReport()">
                    📄 تصدير تقرير
                </button>
                <button class="btn btn-warning" onclick="refreshData()">
                    🔄 تحديث
                </button>
            </div>
            
            <!-- شريط الإحصائيات السريعة -->
            <div style="background: white; padding: 15px; border-radius: 8px; margin-top: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-around; text-align: center; flex-wrap: wrap; gap: 10px;">
                    <div>
                        <div style="font-size: 20px; font-weight: bold; color: #ffc107;">📊</div>
                        <div style="font-size: 12px; color: #6c757d;">إجمالي الطلاب</div>
                        <div style="font-size: 18px; font-weight: bold; color: #007bff;" id="totalStudents">0</div>
                    </div>
                    <div>
                        <div style="font-size: 20px; font-weight: bold; color: #28a745;">✅</div>
                        <div style="font-size: 12px; color: #6c757d;">حضور منتظم</div>
                        <div style="font-size: 18px; font-weight: bold; color: #28a745;" id="regularAttendance">0</div>
                    </div>
                    <div>
                        <div style="font-size: 20px; font-weight: bold; color: #dc3545;">❌</div>
                        <div style="font-size: 12px; color: #6c757d;">يحتاج متابعة</div>
                        <div style="font-size: 18px; font-weight: bold; color: #dc3545;" id="needsFollowup">0</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قسم الملخص الإحصائي -->
        <div class="warnings-section">
            <div class="summary-section">
                <h3>📈 ملخص إنذارات الغياب</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-number first" id="firstWarnings">0</div>
                        <div>🟡 إنذار أول</div>
                        <div style="font-size: 11px; color: #6c757d; margin-top: 5px;">5-10 أيام غياب</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number second" id="secondWarnings">0</div>
                        <div>🟠 إنذار ثاني</div>
                        <div style="font-size: 11px; color: #6c757d; margin-top: 5px;">11-20 يوم غياب</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number final" id="finalWarnings">0</div>
                        <div>🔴 إنذار نهائي</div>
                        <div style="font-size: 11px; color: #6c757d; margin-top: 5px;">أكثر من 20 يوم</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number total" id="totalWarnings">0</div>
                        <div>📊 إجمالي الإنذارات</div>
                        <div style="font-size: 11px; color: #6c757d; margin-top: 5px;">جميع الأنواع</div>
                    </div>
                </div>
            </div>
            
            <!-- رسالة التحميل -->
            <div id="loadingMessage" style="text-align: center; padding: 40px; color: #6c757d;">
                <div style="font-size: 48px; margin-bottom: 20px;">⏳</div>
                <h3>جاري تحميل بيانات الإنذارات...</h3>
                <p>يرجى الانتظار قليلاً</p>
            </div>
            
            <!-- شبكة الإنذارات -->
            <div class="warnings-grid" id="warningsContainer" style="display: none;">
                <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
            </div>
            
            <!-- رسالة عدم وجود نتائج -->
            <div id="noResultsMessage" style="display: none; text-align: center; padding: 40px; color: #6c757d;">
                <div style="font-size: 48px; margin-bottom: 20px;">🔍</div>
                <h3>لا توجد إنذارات تطابق معايير البحث</h3>
                <p>جرب تغيير فلاتر البحث أو مسح النص المدخل</p>
                <button class="btn btn-primary" onclick="clearFilters()">مسح الفلاتر</button>
            </div>
        </div>
        
        <!-- قسم الإجراءات السريعة -->
        <div style="background: #f8f9fa; padding: 20px; border-top: 2px solid #dee2e6;">
            <h4 style="text-align: center; margin-bottom: 15px; color: #495057;">⚡ إجراءات سريعة</h4>
            <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-warning" onclick="sendBulkNotifications()">
                    📢 إرسال إشعارات جماعية
                </button>
                <button class="btn btn-primary" onclick="generateDetailedReport()">
                    📋 تقرير مفصل
                </button>
                <button class="btn btn-success" onclick="exportToExcel()">
                    📊 تصدير إكسل
                </button>
                <button class="btn btn-danger" onclick="scheduleParentMeetings()">
                    👥 جدولة لقاءات أولياء الأمور
                </button>
            </div>
        </div>
        
        <!-- تذييل الصفحة -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; text-align: center;">
            <div style="font-size: 14px; opacity: 0.9;">
                © 2025 مدرسة ضباء المتوسطة للبنين - جميع الحقوق محفوظة
            </div>
            <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">
                آخر تحديث: <span id="lastUpdate"></span>
            </div>
        </div>
    </div>
    
    <!-- تحميل ملف JavaScript -->
    <script src="warnings_script.js"></script>
    
    <!-- إضافات JavaScript خاصة بهذه الصفحة -->
    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Riyadh'
            };
            
            const dateTimeString = now.toLocaleDateString('ar-SA', options);
            document.getElementById('currentDateTime').textContent = `التاريخ والوقت: ${dateTimeString}`;
            document.getElementById('lastUpdate').textContent = now.toLocaleTimeString('ar-SA');
        }
        
        // تحديث الإحصائيات السريعة
        function updateQuickStats() {
            document.getElementById('totalStudents').textContent = '150';
            document.getElementById('regularAttendance').textContent = '127';
            document.getElementById('needsFollowup').textContent = '23';
        }
        
        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('gradeFilter').value = '';
            document.getElementById('warningFilter').value = '';
            document.getElementById('searchInput').value = '';
            filterWarnings();
        }
        
        // تحديث البيانات
        function refreshData() {
            showLoadingMessage();
            setTimeout(() => {
                hideLoadingMessage();
                filterWarnings();
                showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 1500);
        }
        
        // إظهار رسالة التحميل
        function showLoadingMessage() {
            document.getElementById('loadingMessage').style.display = 'block';
            document.getElementById('warningsContainer').style.display = 'none';
            document.getElementById('noResultsMessage').style.display = 'none';
        }
        
        // إخفاء رسالة التحميل
        function hideLoadingMessage() {
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('warningsContainer').style.display = 'grid';
        }
        
        // إجراءات إضافية
        function sendBulkNotifications() {
            showAlert('تم إرسال الإشعارات الجماعية بنجاح', 'success');
        }
        
        function generateDetailedReport() {
            showAlert('جاري إنشاء التقرير المفصل...', 'info');
        }
        
        function exportToExcel() {
            showAlert('جاري تصدير البيانات إلى ملف إكسل...', 'info');
        }
        
        function scheduleParentMeetings() {
            showAlert('تم جدولة لقاءات أولياء الأمور', 'success');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();
            updateQuickStats();
            
            // تحديث التاريخ والوقت كل ثانية
            setInterval(updateDateTime, 1000);
            
            // محاكاة تحميل البيانات
            setTimeout(() => {
                hideLoadingMessage();
                // استدعاء الدوال من ملف JavaScript الخارجي
                if (typeof renderWarnings === 'function') {
                    renderWarnings();
                }
            }, 2000);
        });
    </script>
</body>
</html>
