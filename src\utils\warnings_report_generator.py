#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد تقارير الإنذارات
ينشئ تقارير إنذارات الغياب بتصميم رسمي مطابق للنموذج المطلوب
"""

import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.utils.warnings_manager import WarningsManager
from src.database.database_manager import DatabaseManager

# محاولة استيراد مكتبات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
    from reportlab.lib.units import inch, cm
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

# محاولة استيراد مكتبات دعم العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT_AVAILABLE = True
except ImportError:
    ARABIC_SUPPORT_AVAILABLE = False

class WarningsReportGenerator:
    """مولد تقارير الإنذارات"""
    
    def __init__(self):
        """تهيئة مولد التقارير"""
        self.warnings_manager = WarningsManager()
        self.db_manager = DatabaseManager()
        self.settings = self._load_settings()
    
    def _load_settings(self):
        """تحميل الإعدادات"""
        settings_file = os.path.join(Config.DATA_DIR, "settings.json")
        default_settings = {}
        
        try:
            if os.path.exists(settings_file):
                import json
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    default_settings.update(saved_settings)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
        
        return default_settings
    
    def _process_arabic_text(self, text: str) -> str:
        """معالجة النص العربي"""
        if not text:
            return ""
        
        try:
            if ARABIC_SUPPORT_AVAILABLE:
                reshaped_text = arabic_reshaper.reshape(text)
                bidi_text = get_display(reshaped_text)
                return bidi_text
            else:
                return text
        except Exception:
            return text
    
    def _register_arabic_font(self) -> str:
        """تسجيل خط عربي"""
        try:
            import platform
            
            if platform.system() == "Windows":
                font_paths = [
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/calibri.ttf"
                ]
                
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Arabic', font_path))
                            return 'Arabic'
                        except:
                            continue
            
            return 'Helvetica'
            
        except Exception:
            return 'Helvetica'
    
    def generate_warning_notice(self, student_id: int, warning_data: Dict[str, Any], filename: str) -> bool:
        """إنشاء نموذج إنذار غياب طالب"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة reportlab غير متوفرة")
        
        try:
            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)
            story = []
            
            # تسجيل الخط العربي
            arabic_font = self._register_arabic_font()
            
            # إنشاء الأنماط
            styles = getSampleStyleSheet()
            
            # إنشاء رأس المدرسة مع التصميم المطلوب
            self._create_header(story, arabic_font, styles)
            
            # إنشاء عنوان النموذج
            self._create_title(story, arabic_font, styles, "نموذج إنذار غياب طالب")
            
            # إنشاء معلومات الطالب
            self._create_student_info(story, arabic_font, styles, student_id, warning_data)
            
            # إنشاء جدول الغيابات
            self._create_absence_table(story, arabic_font, styles, student_id)
            
            # إنشاء نص الإنذار
            self._create_warning_text(story, arabic_font, styles, warning_data)
            
            # إنشاء التوقيعات
            self._create_signatures(story, arabic_font, styles)
            
            # بناء المستند
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير الإنذار: {str(e)}")
    
    def _create_header(self, story, arabic_font: str, styles):
        """إنشاء رأس المدرسة"""
        # الحصول على معلومات المدرسة
        school_name = self.settings.get('school_name', Config.SCHOOL_NAME)
        school_address = self.settings.get('school_address', Config.SCHOOL_ADDRESS)
        
        # نمط رأس المدرسة
        header_style = ParagraphStyle(
            'Header',
            parent=styles['Normal'],
            fontName=arabic_font,
            fontSize=14,
            alignment=1,  # توسيط
            spaceAfter=6,
            textColor=colors.black
        )
        
        # إضافة اسم المدرسة
        school_name_text = self._process_arabic_text(school_name)
        story.append(Paragraph(school_name_text, header_style))
        
        # إضافة عنوان المدرسة
        school_address_text = self._process_arabic_text(school_address)
        story.append(Paragraph(school_address_text, header_style))
        
        # خط فاصل
        story.append(Spacer(1, 12))
        
        # إضافة خط أفقي
        line_table = Table([['_' * 80]], colWidths=[7*inch])
        line_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ]))
        story.append(line_table)
        story.append(Spacer(1, 12))
    
    def _create_title(self, story, arabic_font: str, styles, title: str):
        """إنشاء عنوان النموذج"""
        title_style = ParagraphStyle(
            'Title',
            parent=styles['Normal'],
            fontName=arabic_font,
            fontSize=16,
            alignment=1,  # توسيط
            spaceAfter=20,
            textColor=colors.black,
            borderWidth=1,
            borderColor=colors.black,
            borderPadding=8
        )
        
        title_text = self._process_arabic_text(title)
        
        # إنشاء جدول للعنوان مع إطار
        title_table = Table([[title_text]], colWidths=[4*inch])
        title_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 16),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        # توسيط الجدول
        title_wrapper = Table([[title_table]], colWidths=[7*inch])
        title_wrapper.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ]))
        
        story.append(title_wrapper)
        story.append(Spacer(1, 20))
    
    def _create_student_info(self, story, arabic_font: str, styles, student_id: int, warning_data: Dict[str, Any]):
        """إنشاء معلومات الطالب"""
        # الحصول على معلومات الطالب
        student_info = self.db_manager.get_student_by_id(student_id)
        if not student_info:
            student_info = {'name': 'غير معروف', 'grade': 'غير محدد', 'section': 'غير محدد'}
        
        # إنشاء جدول معلومات الطالب
        student_data = [
            [
                self._process_arabic_text("الفصل الدراسي:"),
                self._process_arabic_text("الأول"),
                self._process_arabic_text("الثاني"),
                self._process_arabic_text("الثالث"),
                self._process_arabic_text("الرابع")
            ],
            [
                self._process_arabic_text("اسم الطالب:"),
                "",
                "",
                "",
                self._process_arabic_text(student_info['name'])
            ],
            [
                self._process_arabic_text("الصف:"),
                "",
                "",
                "",
                self._process_arabic_text(student_info['grade'])
            ]
        ]
        
        student_table = Table(student_data, colWidths=[1.5*inch, 1*inch, 1*inch, 1*inch, 2.5*inch])
        student_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            
            # الخط والحجم
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            
            # المحاذاة
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # تنسيق الصف الأول (رؤوس الفصول)
            ('BACKGROUND', (1, 0), (4, 0), colors.lightgrey),
            
            # تنسيق العمود الأول (التسميات)
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            
            # تنسيق خانات الاختيار
            ('BACKGROUND', (1, 1), (3, -1), colors.white),
            
            # ارتفاع الصفوف
            ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.white]),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(student_table)
        story.append(Spacer(1, 20))
    
    def _create_absence_table(self, story, arabic_font: str, styles, student_id: int):
        """إنشاء جدول الغيابات"""
        # الحصول على سجلات الغياب للطالب
        absences = self._get_student_absences(student_id)
        
        # رؤوس الجدول
        headers = [
            self._process_arabic_text("ملاحظات"),
            self._process_arabic_text("31"),
            self._process_arabic_text("30"),
            self._process_arabic_text("29"),
            self._process_arabic_text("28"),
            self._process_arabic_text("27"),
            self._process_arabic_text("26"),
            self._process_arabic_text("25")
        ]
        
        # إنشاء صفوف الجدول
        table_data = [headers]
        
        # صف تاريخ الغياب
        absence_row = [self._process_arabic_text("تاريخ الغياب")]
        for i in range(7):
            absence_row.append("")  # خانات فارغة للتعبئة
        table_data.append(absence_row)
        
        # إنشاء الجدول
        absence_table = Table(table_data, colWidths=[1.2*inch] + [0.7*inch]*7)
        absence_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            
            # الخط والحجم
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            
            # المحاذاة
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # تنسيق الرؤوس
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('BACKGROUND', (0, 1), (0, 1), colors.lightgrey),
            
            # ارتفاع الصفوف
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(absence_table)
        story.append(Spacer(1, 20))
    
    def _create_warning_text(self, story, arabic_font: str, styles, warning_data: Dict[str, Any]):
        """إنشاء نص الإنذار"""
        warning_level = warning_data.get('warning_level', {})
        absence_days = warning_data.get('absence_days', 0)
        
        # نص الإنذار الأول
        warning_style = ParagraphStyle(
            'Warning',
            parent=styles['Normal'],
            fontName=arabic_font,
            fontSize=11,
            alignment=2,  # محاذاة يمين
            spaceAfter=12,
            textColor=colors.black
        )
        
        warning_text1 = self._process_arabic_text(
            f"نود إعلامكم بأن نجلكم/نجلتكم قد تغيب عن المدرسة لمدة {absence_days} أيام بدون عذر مقبول، "
            f"لذا نرجوكم الاهتمام والمتابعة وحث ابنكم على الانتظام في الحضور إلى المدرسة والمحافظة على مستواه الدراسي."
        )
        
        story.append(Paragraph(warning_text1, warning_style))
        
        # إضافة مربع للتوقيع
        signature_table = Table([
            [self._process_arabic_text("الإنذار الأول"), ""],
            [self._process_arabic_text("خطاب الله"), ""],
            [self._process_arabic_text("التاريخ: ___/___/1445"), self._process_arabic_text("ولي الأمر/_______________")]
        ], colWidths=[3*inch, 3*inch])
        
        signature_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(signature_table)
        story.append(Spacer(1, 20))
    
    def _create_signatures(self, story, arabic_font: str, styles):
        """إنشاء منطقة التوقيعات"""
        # الحصول على معلومات المدير
        principal_name = self.settings.get('principal_name', Config.PRINCIPAL_NAME)
        principal_title = self.settings.get('principal_title', Config.PRINCIPAL_TITLE)
        
        # جدول التوقيعات
        signatures_data = [
            [
                self._process_arabic_text("الإنذار الثاني"),
                self._process_arabic_text("الإنذار الثالث")
            ],
            [
                self._process_arabic_text("خطاب الله"),
                self._process_arabic_text("خطاب الله")
            ],
            [
                self._process_arabic_text("التاريخ: ___/___/1445"),
                self._process_arabic_text("التاريخ: ___/___/1445")
            ],
            [
                self._process_arabic_text("ولي الأمر/_______________"),
                self._process_arabic_text("ولي الأمر/_______________")
            ]
        ]
        
        signatures_table = Table(signatures_data, colWidths=[3*inch, 3*inch])
        signatures_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(signatures_table)
        story.append(Spacer(1, 20))
        
        # توقيع المدير
        director_table = Table([
            [self._process_arabic_text(principal_name)],
            [self._process_arabic_text(principal_title)]
        ], colWidths=[3*inch])
        
        director_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        # توسيط جدول المدير
        director_wrapper = Table([[director_table]], colWidths=[7*inch])
        director_wrapper.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ]))
        
        story.append(director_wrapper)
    
    def _get_student_absences(self, student_id: int) -> List[Dict[str, Any]]:
        """الحصول على سجلات غياب الطالب"""
        try:
            # الحصول على الغيابات في آخر 30 يوم
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
            
            absences = self.db_manager.get_student_attendance_records(
                student_id, start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
            )
            
            return absences
            
        except Exception as e:
            print(f"خطأ في الحصول على سجلات الغياب: {e}")
            return []
    
    def generate_warnings_summary_report(self, filename: str) -> bool:
        """إنشاء تقرير ملخص الإنذارات"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة reportlab غير متوفرة")
        
        try:
            # الحصول على جميع الإنذارات النشطة
            all_warnings = self.warnings_manager.check_all_students_warnings()
            active_warnings = [w for w in all_warnings if w.get('needs_warning', False)]
            
            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)
            story = []
            
            # تسجيل الخط العربي
            arabic_font = self._register_arabic_font()
            styles = getSampleStyleSheet()
            
            # إنشاء رأس المدرسة
            self._create_header(story, arabic_font, styles)
            
            # إنشاء عنوان التقرير
            self._create_title(story, arabic_font, styles, "تقرير ملخص إنذارات الغياب")
            
            # إنشاء جدول الإنذارات
            self._create_warnings_summary_table(story, arabic_font, styles, active_warnings)
            
            # إنشاء التوقيعات
            self._create_signatures(story, arabic_font, styles)
            
            # بناء المستند
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير ملخص الإنذارات: {str(e)}")
    
    def _create_warnings_summary_table(self, story, arabic_font: str, styles, warnings: List[Dict[str, Any]]):
        """إنشاء جدول ملخص الإنذارات"""
        # رؤوس الجدول
        headers = [
            self._process_arabic_text("الإجراء المطلوب"),
            self._process_arabic_text("مستوى الإنذار"),
            self._process_arabic_text("أيام الغياب"),
            self._process_arabic_text("الصف"),
            self._process_arabic_text("اسم الطالب"),
            self._process_arabic_text("م")
        ]
        
        # إنشاء بيانات الجدول
        table_data = [headers]
        
        for i, warning in enumerate(warnings, 1):
            warning_level = warning.get('warning_level', {})
            row = [
                self._process_arabic_text(warning_level.get('action', '')),
                self._process_arabic_text(warning_level.get('name', '')),
                str(warning.get('absence_days', 0)),
                self._process_arabic_text(warning.get('student_grade', '')),
                self._process_arabic_text(warning.get('student_name', '')),
                str(i)
            ]
            table_data.append(row)
        
        # إنشاء الجدول
        warnings_table = Table(table_data, colWidths=[1.2*inch, 1*inch, 0.8*inch, 1*inch, 1.5*inch, 0.5*inch])
        warnings_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            
            # الخط والحجم
            ('FONTNAME', (0, 0), (-1, -1), arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            
            # المحاذاة
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # تنسيق الرؤوس
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            
            # ارتفاع الصفوف
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            
            # تلوين الصفوف بالتناوب
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        story.append(warnings_table)
        story.append(Spacer(1, 20))
