#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دعم RTL
اختبار بسيط للتأكد من عمل دعم الاتجاه من اليمين إلى اليسار
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_rtl_support():
    """اختبار دعم RTL"""
    print("🔍 اختبار دعم RTL...")
    
    try:
        from src.utils.rtl_support import rtl_support
        from src.utils.config import Config
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار دعم RTL")
        root.geometry("800x600")
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure((0, 1), weight=1)
        
        # اختبار العنوان
        title = rtl_support.create_rtl_label(main_frame, text="اختبار دعم اللغة العربية والاتجاه RTL", 
                                           font=(rtl_support.get_arabic_font(), 16, 'bold'))
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # اختبار LabelFrame
        test_frame = rtl_support.create_rtl_labelframe(main_frame, text="نموذج اختبار", padding="15")
        test_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        test_frame.columnconfigure(0, weight=1)
        
        # اختبار Entry مع RTL
        rtl_support.create_rtl_label(test_frame, text="اسم الطالب:").grid(row=0, column=1, padx=(10, 0), pady=5, sticky=tk.E)
        name_var = tk.StringVar()
        name_var.set("محمد أحمد علي")
        name_entry = rtl_support.create_rtl_entry(test_frame, textvariable=name_var, width=30)
        name_entry.grid(row=0, column=0, pady=5, sticky=(tk.W, tk.E))
        
        # اختبار Combobox
        rtl_support.create_rtl_label(test_frame, text="الصف الدراسي:").grid(row=1, column=1, padx=(10, 0), pady=5, sticky=tk.E)
        grade_var = tk.StringVar()
        grade_combo = ttk.Combobox(test_frame, textvariable=grade_var, state="readonly", justify='right')
        grade_combo['values'] = list(Config.GRADES.values())
        grade_combo.set("الثاني متوسط")
        grade_combo.grid(row=1, column=0, pady=5, sticky=(tk.W, tk.E))
        
        # اختبار Text widget
        rtl_support.create_rtl_label(test_frame, text="ملاحظات:").grid(row=2, column=1, padx=(10, 0), pady=5, sticky=tk.E)
        text_widget = rtl_support.create_rtl_text(test_frame, width=30, height=4)
        text_widget.insert('1.0', "هذا نص تجريبي باللغة العربية لاختبار دعم الاتجاه من اليمين إلى اليسار.\nيجب أن يظهر النص محاذياً إلى اليمين.")
        text_widget.grid(row=2, column=0, pady=5, sticky=(tk.W, tk.E))
        
        # اختبار Treeview
        tree_frame = rtl_support.create_rtl_labelframe(main_frame, text="جدول اختبار", padding="10")
        tree_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        columns = ('الاسم', 'الصف', 'الشعبة', 'الحالة')
        tree = rtl_support.create_rtl_treeview(tree_frame, columns=columns, show='headings', height=6)
        
        # تكوين الأعمدة
        for col in columns:
            tree.heading(col, text=col, anchor='e')
            tree.column(col, width=150, anchor='e')
        
        # إضافة بيانات تجريبية
        test_data = [
            ('أحمد محمد علي', 'الأول متوسط', 'أ', 'حاضر'),
            ('فاطمة سالم أحمد', 'الثاني متوسط', 'ب', 'غائب'),
            ('عبدالله يوسف محمد', 'الثالث متوسط', 'أ', 'حاضر'),
            ('مريم عبدالرحمن', 'الأول متوسط', 'ج', 'استئذان'),
            ('خالد عبدالعزيز', 'الثاني متوسط', 'أ', 'حاضر')
        ]
        
        for data in test_data:
            tree.insert('', 'end', values=data)
        
        # شريط تمرير (على اليسار في RTL)
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.grid(row=0, column=0, sticky=(tk.N, tk.S))
        tree.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # أزرار اختبار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))
        
        rtl_support.create_rtl_button(buttons_frame, text="اختبار الأزرار", width=15).grid(row=0, column=0, padx=5)
        rtl_support.create_rtl_button(buttons_frame, text="حفظ البيانات", width=15).grid(row=0, column=1, padx=5)
        rtl_support.create_rtl_button(buttons_frame, text="إلغاء", width=15).grid(row=0, column=2, padx=5)
        
        # تطبيق RTL على النافذة كاملة
        rtl_support.apply_rtl_to_window(root)
        
        print("✅ تم إنشاء نافذة اختبار RTL بنجاح")
        print("📝 تحقق من:")
        print("   - محاذاة النصوص إلى اليمين")
        print("   - ترتيب العناصر من اليمين إلى اليسار")
        print("   - عرض النصوص العربية بشكل صحيح")
        print("   - موضع شريط التمرير على اليسار")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار RTL: {str(e)}")
        return False

def test_arabic_fonts():
    """اختبار الخطوط العربية"""
    print("\n🔤 اختبار الخطوط العربية...")
    
    try:
        from src.utils.rtl_support import rtl_support
        import tkinter.font as tkFont
        
        # الحصول على الخطوط المتوفرة
        available_fonts = tkFont.families()
        arabic_fonts = ['Tahoma', 'Arial Unicode MS', 'Arial', 'Segoe UI', 'Microsoft Sans Serif']
        
        print("الخطوط المتوفرة للعربية:")
        for font in arabic_fonts:
            if font in available_fonts:
                print(f"✅ {font} - متوفر")
            else:
                print(f"❌ {font} - غير متوفر")
        
        # الخط المختار
        selected_font = rtl_support.get_arabic_font()
        print(f"\n🎯 الخط المختار: {selected_font}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخطوط: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لاختبار RTL"""
    print("🚀 بدء اختبار دعم RTL...")
    print("=" * 50)
    
    # اختبار الخطوط
    fonts_ok = test_arabic_fonts()
    
    if fonts_ok:
        print("\n🖥️ سيتم فتح نافذة اختبار RTL...")
        print("تحقق من العناصر التالية:")
        print("1. محاذاة النصوص إلى اليمين")
        print("2. ترتيب العناصر من اليمين إلى اليسار")
        print("3. عرض النصوص العربية بوضوح")
        print("4. موضع شريط التمرير على اليسار")
        print("\nأغلق النافذة للمتابعة...")
        
        # اختبار RTL
        rtl_ok = test_rtl_support()
        
        if rtl_ok:
            print("\n🎉 اختبار RTL مكتمل!")
        else:
            print("\n⚠️ فشل في اختبار RTL")
    else:
        print("\n⚠️ مشكلة في الخطوط العربية")

if __name__ == "__main__":
    main()
