#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام
اختبار بسيط للتأكد من عمل جميع مكونات البرنامج
"""

import sys
import os
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد جميع المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        # اختبار المكتبات الأساسية
        import tkinter as tk
        from tkinter import ttk
        print("✅ tkinter - متوفر")
        
        import sqlite3
        print("✅ sqlite3 - متوفر")
        
        # اختبار المكتبات الإضافية
        try:
            import pandas as pd
            print("✅ pandas - متوفر")
        except ImportError:
            print("❌ pandas - غير متوفر")
        
        try:
            import matplotlib.pyplot as plt
            print("✅ matplotlib - متوفر")
        except ImportError:
            print("❌ matplotlib - غير متوفر")
        
        try:
            from sklearn.ensemble import RandomForestClassifier
            print("✅ scikit-learn - متوفر")
        except ImportError:
            print("❌ scikit-learn - غير متوفر")
        
        try:
            from reportlab.lib.pagesizes import A4
            print("✅ reportlab - متوفر")
        except ImportError:
            print("❌ reportlab - غير متوفر")
        
        try:
            import openpyxl
            print("✅ openpyxl - متوفر")
        except ImportError:
            print("❌ openpyxl - غير متوفر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # تهيئة قاعدة البيانات
        db_manager.initialize_database()
        print("✅ تهيئة قاعدة البيانات - نجح")
        
        # اختبار إضافة طالب
        student_data = {
            'student_id': 'TEST001',
            'name': 'طالب تجريبي',
            'grade': 1,
            'class_section': 'أ',
            'phone': '0501234567',
            'parent_phone': '0507654321',
            'address': 'عنوان تجريبي'
        }
        
        student_id = db_manager.add_student(student_data)
        print("✅ إضافة طالب - نجح")
        
        # اختبار الحصول على الطلاب
        students = db_manager.get_students()
        print(f"✅ الحصول على الطلاب - نجح ({len(students)} طالب)")
        
        # اختبار إضافة غياب
        absence_data = {
            'student_id': student_id,
            'absence_date': date.today().strftime("%Y-%m-%d"),
            'absence_type': 'unexcused',
            'reason': 'اختبار النظام',
            'notes': 'غياب تجريبي'
        }
        
        absence_id = db_manager.add_absence(absence_data)
        print("✅ إضافة غياب - نجح")
        
        # اختبار الإحصائيات
        stats = db_manager.get_absence_statistics()
        print(f"✅ الإحصائيات - نجح (إجمالي الغيابات: {stats['total_absences']})")
        
        # تنظيف البيانات التجريبية
        db_manager.delete_absence(absence_id)
        db_manager.delete_student(student_id)
        print("✅ تنظيف البيانات التجريبية - نجح")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    try:
        from src.utils.config import Config
        
        # اختبار الثوابت
        print(f"✅ اسم البرنامج: {Config.APP_NAME}")
        print(f"✅ إصدار البرنامج: {Config.APP_VERSION}")
        print(f"✅ السنة الدراسية الحالية: {Config.get_academic_year()}")
        print(f"✅ الفصل الدراسي الحالي: {Config.SEMESTERS[Config.get_current_semester()]}")
        
        # اختبار إنشاء المجلدات
        Config.ensure_directories()
        print("✅ إنشاء المجلدات - نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {str(e)}")
        return False

def test_export_utils():
    """اختبار أدوات التصدير"""
    print("\n📄 اختبار أدوات التصدير...")
    
    try:
        from src.utils.export_utils import export_utils
        
        # اختبار توفر إمكانيات التصدير
        pdf_available = export_utils.is_pdf_available()
        excel_available = export_utils.is_excel_available()
        
        print(f"✅ تصدير PDF - {'متوفر' if pdf_available else 'غير متوفر'}")
        print(f"✅ تصدير Excel - {'متوفر' if excel_available else 'غير متوفر'}")
        
        # اختبار تصدير CSV (متوفر دائماً)
        test_data = [['طالب 1', 'الأول متوسط', '2025-01-01'], ['طالب 2', 'الثاني متوسط', '2025-01-02']]
        test_headers = ['الاسم', 'الصف', 'التاريخ']
        
        csv_filename = export_utils.get_export_filename('test', 'csv')
        success = export_utils.export_to_csv(test_data, test_headers, csv_filename)
        
        if success and os.path.exists(csv_filename):
            print("✅ تصدير CSV - نجح")
            os.remove(csv_filename)  # حذف الملف التجريبي
        else:
            print("❌ تصدير CSV - فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أدوات التصدير: {str(e)}")
        return False

def test_ai_analytics():
    """اختبار محرك الذكاء الصناعي"""
    print("\n🤖 اختبار محرك الذكاء الصناعي...")
    
    try:
        from src.ai.analytics import analytics_engine
        
        # اختبار تحليل الأنماط
        patterns = analytics_engine.analyze_absence_patterns()
        print(f"✅ تحليل الأنماط - نجح (إجمالي الغيابات: {patterns['total_absences']})")
        
        # اختبار إنشاء تقرير الرؤى
        insights = analytics_engine.generate_insights_report()
        print("✅ تقرير الرؤى - نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الذكاء الصناعي: {str(e)}")
        return False

def test_gui_imports():
    """اختبار استيراد واجهات المستخدم"""
    print("\n🖥️ اختبار واجهات المستخدم...")
    
    try:
        from src.gui.main_window import MainWindow
        print("✅ النافذة الرئيسية - متوفرة")
        
        from src.gui.students_management import StudentsManagementWindow
        print("✅ نافذة إدارة الطلاب - متوفرة")
        
        from src.gui.attendance_recording import AttendanceRecordingWindow
        print("✅ نافذة تسجيل الغياب - متوفرة")
        
        from src.gui.reports_window import ReportsWindow
        print("✅ نافذة التقارير - متوفرة")
        
        from src.gui.ai_insights import AIInsightsWindow
        print("✅ نافذة رؤى الذكاء الصناعي - متوفرة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهات المستخدم: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام...")
    print("=" * 50)
    
    tests = [
        ("استيراد المكتبات", test_imports),
        ("الإعدادات", test_config),
        ("قاعدة البيانات", test_database),
        ("أدوات التصدير", test_export_utils),
        ("محرك الذكاء الصناعي", test_ai_analytics),
        ("واجهات المستخدم", test_gui_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
