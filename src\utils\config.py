#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعدادات البرنامج
يحتوي على جميع الإعدادات والثوابت المستخدمة في البرنامج
"""

import os
from datetime import datetime

class Config:
    """فئة إعدادات البرنامج"""
    
    # معلومات البرنامج
    APP_NAME = "برنامج تسجيل غياب الطلاب الذكي"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "مساعد الذكاء الصناعي"
    
    # مسارات الملفات
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    DATA_DIR = os.path.join(BASE_DIR, "data")
    DATABASE_PATH = os.path.join(DATA_DIR, "attendance.db")
    REPORTS_DIR = os.path.join(DATA_DIR, "reports")
    EXPORTS_DIR = os.path.join(DATA_DIR, "exports")
    
    # إعدادات قاعدة البيانات
    DB_TIMEOUT = 30
    
    # إعدادات الواجهة
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    WINDOW_MIN_WIDTH = 800
    WINDOW_MIN_HEIGHT = 600
    
    # الألوان
    PRIMARY_COLOR = "#2E86AB"
    SECONDARY_COLOR = "#A23B72"
    SUCCESS_COLOR = "#28A745"
    WARNING_COLOR = "#FFC107"
    DANGER_COLOR = "#DC3545"
    LIGHT_COLOR = "#F8F9FA"
    DARK_COLOR = "#343A40"
    
    # الخطوط
    FONT_FAMILY = "Tahoma"  # خط أفضل للعربية
    FONT_SIZE_SMALL = 10
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_TITLE = 16

    # إعدادات RTL واللغة العربية
    RTL_ENABLED = True
    TEXT_DIRECTION = "rtl"  # right-to-left
    DEFAULT_ANCHOR = "e"    # east (يمين)
    DEFAULT_JUSTIFY = "right"
    
    # أنواع الغياب
    ABSENCE_TYPES = {
        "excused": "بعذر",
        "unexcused": "بدون عذر", 
        "permission": "استئذان"
    }
    
    # الفصول الدراسية
    SEMESTERS = {
        1: "الفصل الدراسي الأول",
        2: "الفصل الدراسي الثاني", 
        3: "الفصل الدراسي الثالث"
    }
    
    # الصفوف الدراسية
    GRADES = {
        1: "الأول متوسط",
        2: "الثاني متوسط",
        3: "الثالث متوسط"
    }
    
    # إعدادات التقارير
    REPORT_FORMATS = ["PDF", "Excel"]
    MAX_ABSENCE_DAYS = 30  # الحد الأقصى لأيام الغياب المسموح بها

    # إعدادات الإنذارات
    WARNING_LEVELS = {
        "yellow": {
            "name": "إنذار أولي",
            "days": 3,
            "color": "#FFC107",
            "action": "تنبيه ولي الأمر"
        },
        "orange": {
            "name": "إنذار متوسط",
            "days": 5,
            "color": "#FF8C00",
            "action": "استدعاء ولي الأمر"
        },
        "red": {
            "name": "إنذار نهائي",
            "days": 7,
            "color": "#DC3545",
            "action": "إحالة للإدارة"
        },
        "critical": {
            "name": "حالة حرجة",
            "days": 10,
            "color": "#8B0000",
            "action": "فصل مؤقت"
        }
    }

    # معلومات المدرسة والمدير
    SCHOOL_NAME = "مدرسة ضباء المتوسطة"
    SCHOOL_ADDRESS = "ضباء، المملكة العربية السعودية"
    SCHOOL_PHONE = "014-1234567"
    SCHOOL_EMAIL = "<EMAIL>"

    # معلومات المدير
    PRINCIPAL_NAME = "الأستاذ / عبدالله محمد الأحمد"
    PRINCIPAL_TITLE = "مدير المدرسة"
    PRINCIPAL_SIGNATURE = "التوقيع: ________________"

    # إعدادات الختم
    SCHOOL_STAMP_TEXT = "ختم المدرسة"
    STAMP_PLACEHOLDER = "[ختم المدرسة الرسمي]"
    
    # إعدادات الذكاء الصناعي
    AI_PREDICTION_DAYS = 30  # عدد الأيام للتنبؤ بالغياب
    AI_MIN_DATA_POINTS = 10  # الحد الأدنى من نقاط البيانات للتحليل
    
    @classmethod
    def ensure_directories(cls):
        """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
        directories = [cls.DATA_DIR, cls.REPORTS_DIR, cls.EXPORTS_DIR]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def get_current_semester(cls):
        """تحديد الفصل الدراسي الحالي بناءً على التاريخ"""
        current_month = datetime.now().month
        
        if 9 <= current_month <= 12:  # سبتمبر - ديسمبر
            return 1
        elif 1 <= current_month <= 4:  # يناير - أبريل
            return 2
        else:  # مايو - أغسطس
            return 3
    
    @classmethod
    def get_academic_year(cls):
        """الحصول على السنة الدراسية الحالية"""
        current_date = datetime.now()
        if current_date.month >= 9:
            return f"{current_date.year}-{current_date.year + 1}"
        else:
            return f"{current_date.year - 1}-{current_date.year}"
