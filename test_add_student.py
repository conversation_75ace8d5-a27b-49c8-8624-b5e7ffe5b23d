#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة الطلاب
اختبار مخصص لحل مشكلة إضافة الطلاب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_add_student_functionality():
    """اختبار وظيفة إضافة الطلاب"""
    print("🔍 اختبار وظيفة إضافة الطلاب...")
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.utils.config import Config
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # بيانات طالب تجريبي
        test_student = {
            'student_id': '12345',
            'name': 'أح<PERSON><PERSON> محمد علي',
            'grade': 2,  # الثاني متوسط
            'class_section': 'أ',
            'phone': '0501234567',
            'parent_phone': '0507654321',
            'address': 'الرياض، حي النخيل'
        }
        
        print("📝 بيانات الطالب التجريبي:")
        for key, value in test_student.items():
            print(f"   {key}: {value}")
        
        # محاولة إضافة الطالب
        print("\n💾 محاولة إضافة الطالب...")
        try:
            student_id = db_manager.add_student(test_student)
            print(f"✅ تم إضافة الطالب بنجاح! معرف الطالب: {student_id}")
            
            # التحقق من إضافة الطالب
            students = db_manager.get_students()
            added_student = None
            for student in students:
                if student['student_id'] == test_student['student_id']:
                    added_student = student
                    break
            
            if added_student:
                print("✅ تم التحقق من وجود الطالب في قاعدة البيانات")
                print(f"   الاسم: {added_student['name']}")
                print(f"   الصف: {added_student['grade']}")
                
                # حذف الطالب التجريبي
                db_manager.delete_student(added_student['id'])
                print("🗑️ تم حذف الطالب التجريبي")
            else:
                print("❌ لم يتم العثور على الطالب في قاعدة البيانات")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة الطالب: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def test_student_form():
    """اختبار نموذج إضافة الطالب"""
    print("\n🖥️ اختبار نموذج إضافة الطالب...")
    
    try:
        from src.gui.students_management import StudentsManagementWindow
        from src.utils.themes import theme_manager
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار إضافة الطلاب")
        root.geometry("600x500")
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🧪 اختبار إضافة الطلاب", 
                               font=("Tahoma", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # معلومات الاختبار
        info_text = """
        هذا اختبار لحل مشكلة إضافة الطلاب.
        
        خطوات الاختبار:
        1. انقر على "فتح نافذة إدارة الطلاب"
        2. املأ جميع الحقول المطلوبة:
           • رقم الطالب (أرقام فقط)
           • اسم الطالب (أكثر من حرفين)
           • الصف الدراسي (اختر من القائمة)
           • الشعبة (اختر من القائمة)
           • رقم الهاتف (اختياري)
        3. انقر على "➕ إضافة طالب"
        
        إذا ظهرت رسالة خطأ، ستظهر تفاصيل الخطأ
        في وحدة التحكم (Console).
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.CENTER)
        info_label.pack(pady=(0, 20))
        
        # زر فتح نافذة إدارة الطلاب
        def open_students_window():
            try:
                students_window = StudentsManagementWindow(root)
                print("✅ تم فتح نافذة إدارة الطلاب")
                print("📝 جرب إضافة طالب جديد وراقب الرسائل في وحدة التحكم")
            except Exception as e:
                print(f"❌ خطأ في فتح نافذة إدارة الطلاب: {str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("خطأ", f"خطأ في فتح النافذة: {str(e)}")
        
        students_btn = ttk.Button(main_frame, text="👥 فتح نافذة إدارة الطلاب", 
                                 command=open_students_window)
        students_btn.pack(pady=10)
        
        # زر اختبار قاعدة البيانات
        def test_database():
            success = test_add_student_functionality()
            if success:
                messagebox.showinfo("نجح", "اختبار قاعدة البيانات نجح!")
            else:
                messagebox.showerror("فشل", "اختبار قاعدة البيانات فشل!")
        
        db_btn = ttk.Button(main_frame, text="🗄️ اختبار قاعدة البيانات", 
                           command=test_database)
        db_btn.pack(pady=5)
        
        # معلومات إضافية
        footer_label = ttk.Label(main_frame, 
                                text="راقب وحدة التحكم للحصول على تفاصيل الأخطاء", 
                                font=("Tahoma", 10), foreground="gray")
        footer_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
        print("✅ تم إنشاء نافذة اختبار إضافة الطلاب")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_grades():
    """اختبار إعدادات الصفوف"""
    print("\n📚 اختبار إعدادات الصفوف...")
    
    try:
        from src.utils.config import Config
        
        print("الصفوف المتاحة:")
        for grade_num, grade_name in Config.GRADES.items():
            print(f"   {grade_num}: {grade_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفوف: {str(e)}")
        return False

def test_components():
    """اختبار المكونات"""
    print("\n🧩 اختبار المكونات...")
    
    try:
        from src.gui.components import ModernEntry, ModernCombobox
        from src.utils.config import Config
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        test_frame = ttk.Frame(root)
        
        # اختبار ModernEntry
        entry = ModernEntry(test_frame, "اختبار", placeholder="نص تجريبي")
        entry.set("قيمة اختبار")
        value = entry.get()
        print(f"✅ ModernEntry: {value}")
        
        # اختبار ModernCombobox
        combo = ModernCombobox(test_frame, "اختبار", values=list(Config.GRADES.values()))
        combo.set("الثاني متوسط")
        combo_value = combo.get()
        print(f"✅ ModernCombobox: {combo_value}")
        
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المكونات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لاختبار إضافة الطلاب"""
    print("🚀 بدء اختبار إضافة الطلاب...")
    print("=" * 60)
    
    # اختبار إعدادات الصفوف
    grades_ok = test_config_grades()
    
    # اختبار المكونات
    components_ok = test_components()
    
    # اختبار قاعدة البيانات
    db_ok = test_add_student_functionality()
    
    if grades_ok and components_ok and db_ok:
        print("\n🖥️ سيتم فتح نافذة اختبار إضافة الطلاب...")
        print("تحقق من الميزات التالية:")
        print("1. ملء جميع الحقول المطلوبة")
        print("2. التحقق من رسائل الخطأ")
        print("3. إضافة طالب جديد بنجاح")
        print("4. مراقبة رسائل وحدة التحكم")
        print("\nأغلق النافذة للمتابعة...")
        
        # اختبار النموذج
        form_ok = test_student_form()
        
        if form_ok:
            print("\n🎉 اختبار إضافة الطلاب مكتمل!")
        else:
            print("\n⚠️ فشل في اختبار النموذج")
    else:
        print("\n⚠️ مشكلة في المتطلبات الأساسية")
        if not grades_ok:
            print("   - مشكلة في إعدادات الصفوف")
        if not components_ok:
            print("   - مشكلة في المكونات")
        if not db_ok:
            print("   - مشكلة في قاعدة البيانات")

if __name__ == "__main__":
    main()
