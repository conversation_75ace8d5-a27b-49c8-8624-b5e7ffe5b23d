#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إحصائيات النظام
للتأكد من أن البيانات تظهر بشكل صحيح
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from datetime import datetime, timedelta

def test_statistics():
    """اختبار إحصائيات النظام"""
    
    print("🧪 اختبار إحصائيات النظام")
    print("=" * 50)
    
    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # اختبار إحصائيات الطلاب
        print("\n👥 إحصائيات الطلاب:")
        students = db_manager.get_students()
        print(f"   إجمالي الطلاب: {len(students)}")

        # تجميع الطلاب حسب الصف
        grade_counts = {}
        for student in students:
            grade = f"الصف {student['grade']}"
            grade_counts[grade] = grade_counts.get(grade, 0) + 1

        for grade, count in grade_counts.items():
            print(f"   {grade}: {count} طالب")

        # اختبار إحصائيات الغياب
        print("\n❌ إحصائيات الغياب:")

        # الغياب اليوم
        today = datetime.now().strftime('%Y-%m-%d')
        today_absences = db_manager.get_daily_absences(today)
        print(f"   غيابات اليوم: {len(today_absences)}")

        # الغياب هذا الشهر
        month_start = datetime.now().replace(day=1).strftime('%Y-%m-%d')
        month_absences = db_manager.get_absences_by_date_range(month_start, today)
        print(f"   غيابات الشهر: {len(month_absences)}")

        # الطلاب الأكثر غياباً
        top_absent = db_manager.get_top_absent_students(limit=5)
        print(f"   الطلاب الأكثر غياباً: {len(top_absent)}")
        
        # اختبار إحصائيات الإنذارات
        print("\n⚠️ إحصائيات الإنذارات:")
        
        # إنذارات نشطة (محاكاة)
        active_warnings = 0
        first_warnings = 0
        second_warnings = 0
        final_warnings = 0
        
        # حساب الإنذارات بناءً على عدد الغيابات
        for student in students:
            student_absences = db_manager.get_student_absences(student['id'])
            absence_count = len(student_absences)
            
            if absence_count >= 5:
                active_warnings += 1
                if absence_count >= 15:
                    final_warnings += 1
                elif absence_count >= 10:
                    second_warnings += 1
                else:
                    first_warnings += 1
        
        print(f"   إنذارات نشطة: {active_warnings}")
        print(f"   إنذار أول: {first_warnings}")
        print(f"   إنذار ثاني: {second_warnings}")
        print(f"   إنذار نهائي: {final_warnings}")
        
        # عرض تفاصيل بعض الطلاب
        print("\n📋 تفاصيل الطلاب (عينة):")
        for i, student in enumerate(students[:5]):
            student_absences = db_manager.get_student_absences(student['id'])
            print(f"   {student['student_id']} - {student['name']}")
            print(f"      الصف: {student['grade']} - الشعبة: {student['class_section']}")
            print(f"      عدد الغيابات: {len(student_absences)}")
            print()
        
        print("✅ اختبار الإحصائيات مكتمل!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإحصائيات: {str(e)}")
        import traceback
        traceback.print_exc()

def test_database_methods():
    """اختبار دوال قاعدة البيانات"""
    
    print("\n🔧 اختبار دوال قاعدة البيانات:")
    print("-" * 30)
    
    try:
        db_manager = DatabaseManager()
        
        # اختبار الحصول على جميع الطلاب
        students = db_manager.get_students()
        print(f"✅ get_students(): {len(students)} طالب")

        # اختبار البحث عن طالب
        if students:
            first_student = students[0]
            found_student = db_manager.get_student_by_id(first_student['student_id'])
            if found_student:
                print(f"✅ get_student_by_id(): وُجد الطالب {found_student['name']}")
            else:
                print("❌ get_student_by_id(): لم يُعثر على الطالب")

        # اختبار الحصول على غيابات طالب
        if students:
            student_id = students[0]['id']
            absences = db_manager.get_student_absences(student_id)
            print(f"✅ get_student_absences(): {len(absences)} غياب للطالب الأول")

        # اختبار الحصول على غيابات يوم معين
        today = datetime.now().strftime('%Y-%m-%d')
        daily_absences = db_manager.get_daily_absences(today)
        print(f"✅ get_daily_absences(): {len(daily_absences)} غياب اليوم")
        
        print("✅ جميع دوال قاعدة البيانات تعمل بشكل صحيح!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_statistics()
    test_database_methods()
    
    print("\n" + "=" * 50)
    print("🎯 ملخص الاختبار:")
    print("   - تم اختبار إحصائيات الطلاب والغياب والإنذارات")
    print("   - تم التحقق من دوال قاعدة البيانات")
    print("   - النظام جاهز للاستخدام!")
    print("\n💡 إذا كانت الإحصائيات لا تزال تظهر صفر في البرنامج،")
    print("   تأكد من إعادة تشغيل البرنامج أو تحديث البيانات.")
