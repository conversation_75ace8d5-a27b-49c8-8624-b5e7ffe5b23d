#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير PDF مع دعم كامل للغة العربية
"""

import sys
import os
sys.path.append('src')

from utils.export_utils import ExportUtils
from datetime import datetime

def test_arabic_pdf():
    """اختبار تصدير PDF مع دعم كامل للغة العربية"""

    print("🚀 بدء اختبار دعم اللغة العربية في PDF...")

    # إنشاء بيانات تجريبية عربية شاملة
    headers = ['التاريخ', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'نوع الغياب', 'السبب']

    data = [
        ['2025-08-01', '1001', 'أحمد محمد علي', 'الأول متوسط', 'أ', 'غياب مبرر', 'مرض'],
        ['2025-08-01', '1002', 'فاطمة عبدالله حسن', 'الثاني متوسط', 'ب', 'غياب غير مبرر', 'بدون عذر'],
        ['2025-08-01', '1003', 'محمد أحمد سالم', 'الثالث متوسط', 'ج', 'إذن', 'ظروف عائلية'],
        ['2025-08-01', '1004', 'عائشة سالم خالد', 'الأول متوسط', 'أ', 'غياب مبرر', 'مرض'],
        ['2025-08-01', '1005', 'علي حسن محمود', 'الثاني متوسط', 'ب', 'غياب غير مبرر', 'بدون عذر'],
        ['2025-08-01', '1006', 'نورا خالد أحمد', 'الثالث متوسط', 'ج', 'إذن', 'موعد طبي'],
        ['2025-08-01', '1007', 'يوسف إبراهيم علي', 'الأول متوسط', 'د', 'غياب مبرر', 'ظروف عائلية'],
        ['2025-08-01', '1008', 'مريم عبدالعزيز', 'الثاني متوسط', 'أ', 'غياب غير مبرر', 'بدون عذر']
    ]

    title = "تقرير الغياب اليومي"

    # إنشاء مجلد التقارير إذا لم يكن موجوداً
    os.makedirs('data/reports', exist_ok=True)

    # اختبار التصدير
    export_utils = ExportUtils()

    try:
        filename = f"data/reports/arabic_support_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        print("📝 إنشاء تقرير PDF بالعربية...")
        print(f"📁 اسم الملف: {filename}")
        print(f"📊 عدد السجلات: {len(data)}")
        print(f"🏷️ عنوان التقرير: {title}")

        success = export_utils.export_to_pdf(data, headers, title, filename)

        if success:
            print("\n✅ تم إنشاء ملف PDF بنجاح!")
            print(f"📄 مسار الملف: {os.path.abspath(filename)}")
            print("🎯 الملف يحتوي على:")
            print("   • عنوان باللغة العربية")
            print("   • رؤوس جدول عربية")
            print("   • أسماء طلاب عربية")
            print("   • بيانات كاملة بالعربية")

            # فتح الملف للعرض
            import platform
            abs_filename = os.path.abspath(filename)
            if platform.system() == "Windows":
                os.startfile(abs_filename)
            elif platform.system() == "Darwin":
                os.system(f"open '{abs_filename}'")
            else:
                os.system(f"xdg-open '{abs_filename}'")

            print("🔍 تم فتح الملف للعرض والمراجعة")
            print("\n🎉 اختبار دعم اللغة العربية مكتمل!")
        else:
            print("❌ فشل في إنشاء ملف PDF")

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_arabic_pdf()
