#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التقارير
تحتوي على واجهات عرض التقارير المختلفة وإمكانية التصفية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date, timedelta
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.database.database_manager import DatabaseManager
from src.utils.export_utils import export_utils
from src.utils.rtl_support import rtl_support

try:
    from tkcalendar import DateEntry
except ImportError:
    DateEntry = None

class ReportsWindow:
    """فئة نافذة التقارير"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة التقارير"""
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.current_report_data = []
        self.setup_window()
        self.create_interface()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("التقارير والإحصائيات")
        self.window.geometry("1400x900")
        self.window.minsize(1200, 800)
        
        # توسيط النافذة
        self.center_window()
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان
        arabic_font = rtl_support.get_arabic_font()
        title_label = rtl_support.create_rtl_label(main_frame, text="التقارير والإحصائيات", font=(arabic_font, 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # منطقة إعدادات التقرير
        self.create_report_settings(main_frame)

        # منطقة عرض التقرير
        self.create_report_display(main_frame)

        # أزرار العمليات
        self.create_action_buttons(main_frame)

        # تطبيق إعدادات RTL
        rtl_support.apply_rtl_to_window(main_frame)
    
    def create_report_settings(self, parent):
        """إنشاء منطقة إعدادات التقرير"""
        settings_frame = ttk.LabelFrame(parent, text="إعدادات التقرير", padding="15")
        settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        settings_frame.columnconfigure((1, 3, 5), weight=1)
        
        # نوع التقرير
        ttk.Label(settings_frame, text="نوع التقرير:").grid(row=0, column=0, padx=(0, 5), sticky=tk.W)
        self.report_type_var = tk.StringVar()
        self.report_type_var.trace('w', self.on_report_type_change)
        report_combo = ttk.Combobox(settings_frame, textvariable=self.report_type_var, 
                                  width=20, state="readonly")
        report_combo['values'] = [
            'تقرير يومي',
            'تقرير أسبوعي', 
            'تقرير شهري',
            'تقرير فصلي',
            'تقرير سنوي',
            'تقرير مخصص'
        ]
        report_combo.set('تقرير يومي')
        report_combo.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        # تاريخ البداية
        ttk.Label(settings_frame, text="من تاريخ:").grid(row=0, column=2, padx=(15, 5), sticky=tk.W)
        self.start_date_var = tk.StringVar()
        if DateEntry:
            self.start_date_entry = DateEntry(settings_frame, textvariable=self.start_date_var,
                                            date_pattern='yyyy-mm-dd', width=12)
            self.start_date_entry.set_date(date.today())
        else:
            self.start_date_var.set(datetime.now().strftime("%Y-%m-%d"))
            self.start_date_entry = ttk.Entry(settings_frame, textvariable=self.start_date_var, width=15)
        self.start_date_entry.grid(row=0, column=3, padx=5, sticky=(tk.W, tk.E))
        
        # تاريخ النهاية
        ttk.Label(settings_frame, text="إلى تاريخ:").grid(row=0, column=4, padx=(15, 5), sticky=tk.W)
        self.end_date_var = tk.StringVar()
        if DateEntry:
            self.end_date_entry = DateEntry(settings_frame, textvariable=self.end_date_var,
                                          date_pattern='yyyy-mm-dd', width=12)
            self.end_date_entry.set_date(date.today())
        else:
            self.end_date_var.set(datetime.now().strftime("%Y-%m-%d"))
            self.end_date_entry = ttk.Entry(settings_frame, textvariable=self.end_date_var, width=15)
        self.end_date_entry.grid(row=0, column=5, padx=5, sticky=(tk.W, tk.E))
        
        # الصف الدراسي
        ttk.Label(settings_frame, text="الصف:").grid(row=1, column=0, padx=(0, 5), pady=(10, 0), sticky=tk.W)
        self.grade_var = tk.StringVar()
        grade_combo = ttk.Combobox(settings_frame, textvariable=self.grade_var, 
                                 width=20, state="readonly")
        grade_combo['values'] = ['الكل'] + list(Config.GRADES.values())
        grade_combo.set('الكل')
        grade_combo.grid(row=1, column=1, padx=5, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # نوع الغياب
        ttk.Label(settings_frame, text="نوع الغياب:").grid(row=1, column=2, padx=(15, 5), pady=(10, 0), sticky=tk.W)
        self.absence_type_var = tk.StringVar()
        absence_combo = ttk.Combobox(settings_frame, textvariable=self.absence_type_var, 
                                   width=20, state="readonly")
        absence_combo['values'] = ['الكل'] + list(Config.ABSENCE_TYPES.values())
        absence_combo.set('الكل')
        absence_combo.grid(row=1, column=3, padx=5, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # الفصل الدراسي
        ttk.Label(settings_frame, text="الفصل الدراسي:").grid(row=1, column=4, padx=(15, 5), pady=(10, 0), sticky=tk.W)
        self.semester_var = tk.StringVar()
        semester_combo = ttk.Combobox(settings_frame, textvariable=self.semester_var, 
                                    width=20, state="readonly")
        semester_combo['values'] = ['الحالي'] + list(Config.SEMESTERS.values())
        semester_combo.set('الحالي')
        semester_combo.grid(row=1, column=5, padx=5, pady=(10, 0), sticky=(tk.W, tk.E))
    
    def create_report_display(self, parent):
        """إنشاء منطقة عرض التقرير"""
        display_frame = ttk.LabelFrame(parent, text="عرض التقرير", padding="10")
        display_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        display_frame.columnconfigure(0, weight=1)
        display_frame.rowconfigure(1, weight=1)
        
        # منطقة الإحصائيات السريعة
        self.create_quick_stats(display_frame)
        
        # جدول التقرير
        self.create_report_table(display_frame)
    
    def create_quick_stats(self, parent):
        """إنشاء منطقة الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(parent, text="إحصائيات سريعة", padding="10")
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        stats_frame.columnconfigure((0, 1, 2, 3, 4), weight=1)
        
        # إنشاء تسميات الإحصائيات
        self.stats_labels = {}
        
        # إجمالي الغيابات
        self.stats_labels['total'] = ttk.Label(stats_frame, text="إجمالي الغيابات: 0", 
                                             font=('Arial', 12, 'bold'))
        self.stats_labels['total'].grid(row=0, column=0, padx=5, pady=5)
        
        # الغيابات بعذر
        self.stats_labels['excused'] = ttk.Label(stats_frame, text="بعذر: 0", 
                                                font=('Arial', 12))
        self.stats_labels['excused'].grid(row=0, column=1, padx=5, pady=5)
        
        # الغيابات بدون عذر
        self.stats_labels['unexcused'] = ttk.Label(stats_frame, text="بدون عذر: 0", 
                                                 font=('Arial', 12))
        self.stats_labels['unexcused'].grid(row=0, column=2, padx=5, pady=5)
        
        # الاستئذانات
        self.stats_labels['permission'] = ttk.Label(stats_frame, text="استئذان: 0", 
                                                   font=('Arial', 12))
        self.stats_labels['permission'].grid(row=0, column=3, padx=5, pady=5)
        
        # عدد الطلاب المتغيبين
        self.stats_labels['students'] = ttk.Label(stats_frame, text="طلاب متغيبون: 0", 
                                                 font=('Arial', 12))
        self.stats_labels['students'].grid(row=0, column=4, padx=5, pady=5)
    
    def create_report_table(self, parent):
        """إنشاء جدول التقرير"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # إنشاء جدول التقرير
        columns = ('التاريخ', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'نوع الغياب', 'السبب')
        self.report_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # تكوين الأعمدة
        column_widths = {
            'التاريخ': 100,
            'رقم الطالب': 100,
            'اسم الطالب': 150,
            'الصف': 100,
            'الشعبة': 80,
            'نوع الغياب': 100,
            'السبب': 200
        }
        
        for col in columns:
            self.report_tree.heading(col, text=col)
            self.report_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.report_tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.report_tree.xview)
        self.report_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # ترتيب العناصر
        self.report_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=3, column=0, pady=(15, 0))
        
        # أزرار العمليات
        ttk.Button(buttons_frame, text="إنشاء التقرير", command=self.generate_report, 
                  width=15).grid(row=0, column=0, padx=5)
        ttk.Button(buttons_frame, text="تصدير PDF", command=self.export_pdf, 
                  width=15).grid(row=0, column=1, padx=5)
        ttk.Button(buttons_frame, text="تصدير Excel", command=self.export_excel, 
                  width=15).grid(row=0, column=2, padx=5)
        ttk.Button(buttons_frame, text="طباعة", command=self.print_report, 
                  width=15).grid(row=0, column=3, padx=5)
        ttk.Button(buttons_frame, text="مسح التقرير", command=self.clear_report,
                  width=15).grid(row=0, column=4, padx=5)

    # ==================== دوال العمليات ====================

    def on_report_type_change(self, *args):
        """التعامل مع تغيير نوع التقرير"""
        report_type = self.report_type_var.get()
        today = date.today()

        if report_type == 'تقرير يومي':
            self.start_date_var.set(today.strftime("%Y-%m-%d"))
            self.end_date_var.set(today.strftime("%Y-%m-%d"))
        elif report_type == 'تقرير أسبوعي':
            # بداية الأسبوع (الأحد)
            start_of_week = today - timedelta(days=today.weekday() + 1)
            end_of_week = start_of_week + timedelta(days=6)
            self.start_date_var.set(start_of_week.strftime("%Y-%m-%d"))
            self.end_date_var.set(end_of_week.strftime("%Y-%m-%d"))
        elif report_type == 'تقرير شهري':
            # بداية الشهر
            start_of_month = today.replace(day=1)
            # نهاية الشهر
            if today.month == 12:
                end_of_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_of_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
            self.start_date_var.set(start_of_month.strftime("%Y-%m-%d"))
            self.end_date_var.set(end_of_month.strftime("%Y-%m-%d"))
        elif report_type == 'تقرير فصلي':
            # تحديد بداية ونهاية الفصل الحالي
            current_semester = Config.get_current_semester()
            current_year = Config.get_academic_year().split('-')[0]

            if current_semester == 1:
                start_date = f"{current_year}-09-01"
                end_date = f"{current_year}-12-31"
            elif current_semester == 2:
                start_date = f"{int(current_year)+1}-01-01"
                end_date = f"{int(current_year)+1}-04-30"
            else:  # الفصل الثالث
                start_date = f"{int(current_year)+1}-05-01"
                end_date = f"{int(current_year)+1}-06-30"

            self.start_date_var.set(start_date)
            self.end_date_var.set(end_date)
        elif report_type == 'تقرير سنوي':
            # السنة الدراسية الحالية
            academic_year = Config.get_academic_year()
            start_year = academic_year.split('-')[0]
            end_year = academic_year.split('-')[1]

            self.start_date_var.set(f"{start_year}-09-01")
            self.end_date_var.set(f"{end_year}-06-30")

    def get_selected_grade(self):
        """الحصول على الصف المحدد"""
        grade_name = self.grade_var.get()
        if grade_name == 'الكل':
            return None

        for grade_num, name in Config.GRADES.items():
            if name == grade_name:
                return grade_num
        return None

    def get_selected_absence_type(self):
        """الحصول على نوع الغياب المحدد"""
        absence_type_name = self.absence_type_var.get()
        if absence_type_name == 'الكل':
            return None

        for key, name in Config.ABSENCE_TYPES.items():
            if name == absence_type_name:
                return key
        return None

    def get_selected_semester(self):
        """الحصول على الفصل الدراسي المحدد"""
        semester_name = self.semester_var.get()
        if semester_name == 'الحالي':
            return self.db_manager.get_current_semester_id()

        for semester_num, name in Config.SEMESTERS.items():
            if name == semester_name:
                # البحث عن الفصل في قاعدة البيانات
                semesters = self.db_manager.get_semesters()
                for semester in semesters:
                    if semester['semester_number'] == semester_num:
                        return semester['id']
        return None

    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # التحقق من صحة التواريخ
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            except ValueError:
                messagebox.showerror("خطأ", "تاريخ غير صحيح. يرجى استخدام تنسيق YYYY-MM-DD")
                return

            if start_date_obj > end_date_obj:
                messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return

            # الحصول على المعايير
            grade = self.get_selected_grade()
            absence_type = self.get_selected_absence_type()
            semester_id = self.get_selected_semester()

            # مسح البيانات الحالية
            for item in self.report_tree.get_children():
                self.report_tree.delete(item)

            self.current_report_data = []

            # الحصول على البيانات من قاعدة البيانات
            current_date = start_date_obj
            total_absences = 0
            excused_count = 0
            unexcused_count = 0
            permission_count = 0
            absent_students = set()

            while current_date <= end_date_obj:
                daily_absences = self.db_manager.get_daily_absences(
                    current_date.strftime("%Y-%m-%d"), grade
                )

                for absence in daily_absences:
                    # تطبيق تصفية نوع الغياب
                    if absence_type and absence['absence_type'] != absence_type:
                        continue

                    # إضافة البيانات إلى التقرير
                    grade_name = Config.GRADES.get(absence['grade'], str(absence['grade']))
                    absence_type_name = Config.ABSENCE_TYPES.get(absence['absence_type'], absence['absence_type'])

                    row_data = (
                        absence['absence_date'],
                        absence['student_id'],
                        absence['student_name'],
                        grade_name,
                        absence['class_section'] or '',
                        absence_type_name,
                        absence.get('reason', '')
                    )

                    self.report_tree.insert('', 'end', values=row_data)
                    self.current_report_data.append(row_data)

                    # تحديث الإحصائيات
                    total_absences += 1
                    absent_students.add(absence['student_id'])

                    if absence['absence_type'] == 'excused':
                        excused_count += 1
                    elif absence['absence_type'] == 'unexcused':
                        unexcused_count += 1
                    elif absence['absence_type'] == 'permission':
                        permission_count += 1

                current_date += timedelta(days=1)

            # تحديث الإحصائيات
            self.update_statistics(total_absences, excused_count, unexcused_count,
                                 permission_count, len(absent_students))

            messagebox.showinfo("نجح", f"تم إنشاء التقرير بنجاح. إجمالي السجلات: {total_absences}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

    def update_statistics(self, total, excused, unexcused, permission, students):
        """تحديث الإحصائيات السريعة"""
        self.stats_labels['total'].config(text=f"إجمالي الغيابات: {total}")
        self.stats_labels['excused'].config(text=f"بعذر: {excused}")
        self.stats_labels['unexcused'].config(text=f"بدون عذر: {unexcused}")
        self.stats_labels['permission'].config(text=f"استئذان: {permission}")
        self.stats_labels['students'].config(text=f"طلاب متغيبون: {students}")

    def export_pdf(self):
        """تصدير التقرير إلى PDF"""
        if not self.current_report_data:
            messagebox.showwarning("تحذير", "لا يوجد بيانات للتصدير. يرجى إنشاء التقرير أولاً")
            return

        if not export_utils.is_pdf_available():
            messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة. يرجى تثبيتها باستخدام:\npip install reportlab")
            return

        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ التقرير كـ PDF"
            )

            if filename:
                # إعداد البيانات للتصدير
                headers = ['التاريخ', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'نوع الغياب', 'السبب']
                title = f"تقرير الغياب - {self.report_type_var.get()}"

                # تصدير إلى PDF
                success = export_utils.export_to_pdf(self.current_report_data, headers, title, filename)

                if success:
                    messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filename}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير التقرير")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير PDF: {str(e)}")

    def export_excel(self):
        """تصدير التقرير إلى Excel"""
        if not self.current_report_data:
            messagebox.showwarning("تحذير", "لا يوجد بيانات للتصدير. يرجى إنشاء التقرير أولاً")
            return

        if not export_utils.is_excel_available():
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متوفرة. يرجى تثبيتها باستخدام:\npip install openpyxl")
            return

        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ التقرير كـ Excel"
            )

            if filename:
                # إعداد البيانات للتصدير
                headers = ['التاريخ', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'نوع الغياب', 'السبب']
                title = f"تقرير الغياب - {self.report_type_var.get()}"

                # تصدير إلى Excel
                success = export_utils.export_to_excel(self.current_report_data, headers, title, filename)

                if success:
                    messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filename}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير التقرير")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير Excel: {str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        if not self.current_report_data:
            messagebox.showwarning("تحذير", "لا يوجد بيانات للطباعة. يرجى إنشاء التقرير أولاً")
            return

        if not export_utils.is_pdf_available():
            messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة للطباعة. يرجى تثبيتها باستخدام:\npip install reportlab")
            return

        try:
            # إعداد البيانات للطباعة
            headers = ['التاريخ', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'نوع الغياب', 'السبب']
            title = f"تقرير الغياب - {self.report_type_var.get()}"

            # طباعة التقرير
            success = export_utils.print_report(self.current_report_data, headers, title)

            if success:
                messagebox.showinfo("نجح", "تم إرسال التقرير للطباعة")
            else:
                messagebox.showerror("خطأ", "فشل في طباعة التقرير")

        except Exception as e:
            error_msg = str(e)
            if "تم إنشاء الملف" in error_msg or "تم فتح الملف" in error_msg:
                messagebox.showinfo("معلومات", error_msg)
            else:
                messagebox.showerror("خطأ", f"خطأ في الطباعة: {error_msg}")

    def clear_report(self):
        """مسح التقرير"""
        # مسح جدول التقرير
        for item in self.report_tree.get_children():
            self.report_tree.delete(item)

        # مسح البيانات
        self.current_report_data = []

        # إعادة تعيين الإحصائيات
        self.update_statistics(0, 0, 0, 0, 0)

    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        self.window.destroy()

# دالة لفتح نافذة التقارير
def open_reports_window(parent=None):
    """فتح نافذة التقارير"""
    return ReportsWindow(parent)
