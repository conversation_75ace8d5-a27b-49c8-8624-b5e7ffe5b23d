#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الطلاب
تحتوي على واجهات إضافة وتعديل وحذف الطلاب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.database.database_manager import DatabaseManager
from src.utils.rtl_support import rtl_support
from src.utils.themes import theme_manager
from src.gui.components import ModernCard, ModernEntry, ModernCombobox, ModernTreeview, ModernButton, ConfirmDialog, LoadingDialog

class StudentsManagementWindow:
    """فئة نافذة إدارة الطلاب"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة إدارة الطلاب"""
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.current_student_id = None
        self.setup_window()
        self.create_interface()
        self.load_students()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الطلاب")
        self.window.geometry("1000x700")
        self.window.minsize(800, 600)
        
        # توسيط النافذة
        self.center_window()
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة الطلاب"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)

        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # منطقة البحث والتصفية
        self.create_search_area(main_frame)

        # المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        content_frame.columnconfigure((0, 1), weight=1)
        content_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # قائمة الطلاب (على اليمين في RTL)
        self.create_students_list(content_frame)

        # منطقة تفاصيل الطالب (على اليسار في RTL)
        self.create_student_details(content_frame)

        # أزرار العمليات
        self.create_action_buttons(main_frame)

        # تطبيق إعدادات RTL
        rtl_support.apply_rtl_to_window(main_frame)

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent, style='Sidebar.TFrame', padding="15")
        toolbar_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        toolbar_frame.columnconfigure(1, weight=1)

        # العنوان مع الأيقونة
        title_frame = ttk.Frame(toolbar_frame)
        title_frame.grid(row=0, column=0, sticky=tk.E)

        icon_label = rtl_support.create_rtl_label(title_frame, text="👥", font=("Arial", 20))
        icon_label.grid(row=0, column=0, padx=(0, 10))

        title_label = rtl_support.create_rtl_label(title_frame, text="إدارة الطلاب", style='Title.TLabel')
        title_label.grid(row=0, column=1)

        # إحصائيات سريعة
        stats_frame = ttk.Frame(toolbar_frame)
        stats_frame.grid(row=0, column=1, padx=(20, 0))

        self.total_students_label = rtl_support.create_rtl_label(stats_frame, text="إجمالي الطلاب: 0", style='Info.TLabel')
        self.total_students_label.grid(row=0, column=0, padx=(0, 20))

        self.selected_student_label = rtl_support.create_rtl_label(stats_frame, text="لم يتم تحديد طالب", style='Info.TLabel')
        self.selected_student_label.grid(row=0, column=1)

        # أزرار سريعة
        quick_buttons_frame = ttk.Frame(toolbar_frame)
        quick_buttons_frame.grid(row=0, column=2, sticky=tk.W)

        ModernButton(quick_buttons_frame, text="➕ جديد", command=self.add_student,
                    style="Success.TButton").grid(row=0, column=0, padx=2)
        ModernButton(quick_buttons_frame, text="🔄 تحديث", command=self.load_students,
                    style="TButton").grid(row=0, column=1, padx=2)
    
    def create_search_area(self, parent):
        """إنشاء منطقة البحث والتصفية المحسنة"""
        search_card = ModernCard(parent, title="🔍 البحث والتصفية")
        search_card.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        search_content = search_card.content_frame
        search_content.columnconfigure((1, 3), weight=1)

        # البحث بالاسم أو رقم الطالب
        self.search_entry = ModernEntry(search_content, "البحث في الطلاب", placeholder="ادخل اسم الطالب أو رقمه...")
        self.search_entry.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        self.search_entry.textvariable.trace('w', self.on_search_change)

        # تصفية بالصف
        self.grade_combo = ModernCombobox(search_content, "الصف الدراسي",
                                        values=['الكل'] + list(Config.GRADES.values()))
        self.grade_combo.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        self.grade_combo.set('الكل')
        self.grade_combo.textvariable.trace('w', self.on_search_change)

        # تصفية بالشعبة
        self.section_combo = ModernCombobox(search_content, "الشعبة",
                                          values=['الكل', 'أ', 'ب', 'ج', 'د'])
        self.section_combo.grid(row=1, column=1, sticky=(tk.W, tk.E))
        self.section_combo.set('الكل')
        self.section_combo.textvariable.trace('w', self.on_search_change)

        # أزرار البحث
        buttons_frame = ttk.Frame(search_content)
        buttons_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0), sticky=tk.E)

        ModernButton(buttons_frame, text="🔍 بحث", command=self.search_students,
                    style="Primary.TButton").grid(row=0, column=0, padx=(0, 5))
        ModernButton(buttons_frame, text="🗑️ مسح", command=self.clear_search,
                    style="TButton").grid(row=0, column=1)
    
    def create_students_list(self, parent):
        """إنشاء قائمة الطلاب المحسنة"""
        list_card = ModernCard(parent, title="📋 قائمة الطلاب")
        # في RTL، قائمة الطلاب على اليمين (column=1)
        list_card.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))

        # إنشاء جدول الطلاب مع بحث محسن
        columns = ('رقم الطالب', 'الاسم', 'الصف', 'الشعبة', 'الهاتف')
        self.students_table = ModernTreeview(list_card.content_frame, columns)
        self.students_table.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_card.content_frame.columnconfigure(0, weight=1)
        list_card.content_frame.rowconfigure(0, weight=1)

        # ربط حدث التحديد
        self.students_table.tree.bind('<<TreeviewSelect>>', self.on_student_select)

        # قائمة سياق (Right-click menu)
        self.create_context_menu()
    
    def create_student_details(self, parent):
        """إنشاء منطقة تفاصيل الطالب المحسنة"""
        details_card = ModernCard(parent, title="📝 تفاصيل الطالب")
        # في RTL، تفاصيل الطالب على اليسار (column=0)
        details_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        details_content = details_card.content_frame
        details_content.columnconfigure(0, weight=1)

        # حقول البيانات المحسنة
        self.student_entries = {}

        # رقم الطالب
        self.student_entries['student_id'] = ModernEntry(details_content, "رقم الطالب", placeholder="مثال: 12345")
        self.student_entries['student_id'].grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # اسم الطالب
        self.student_entries['name'] = ModernEntry(details_content, "اسم الطالب", placeholder="الاسم الكامل للطالب")
        self.student_entries['name'].grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # الصف الدراسي
        self.student_entries['grade'] = ModernCombobox(details_content, "الصف الدراسي",
                                                     values=list(Config.GRADES.values()))
        self.student_entries['grade'].grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # الشعبة
        self.student_entries['class_section'] = ModernCombobox(details_content, "الشعبة",
                                                             values=['أ', 'ب', 'ج', 'د'])
        self.student_entries['class_section'].grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # رقم الهاتف
        self.student_entries['phone'] = ModernEntry(details_content, "رقم الهاتف", placeholder="05xxxxxxxx")
        self.student_entries['phone'].grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # رقم هاتف ولي الأمر
        self.student_entries['parent_phone'] = ModernEntry(details_content, "هاتف ولي الأمر", placeholder="05xxxxxxxx")
        self.student_entries['parent_phone'].grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # العنوان
        address_frame = ttk.Frame(details_content)
        address_frame.grid(row=6, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        address_frame.columnconfigure(0, weight=1)

        rtl_support.create_rtl_label(address_frame, text="العنوان:", style='Info.TLabel').grid(row=0, column=0, sticky=tk.E, pady=(0, 5))
        self.address_text = rtl_support.create_rtl_text(address_frame, width=30, height=3)
        self.address_text.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # معلومات إضافية
        info_frame = ttk.Frame(details_content)
        info_frame.grid(row=7, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.student_info_label = rtl_support.create_rtl_label(info_frame, text="اختر طالباً لعرض تفاصيله", style='Info.TLabel')
        self.student_info_label.grid(row=0, column=0, sticky=tk.E)
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات المحسنة"""
        buttons_card = ModernCard(parent, title="⚡ العمليات")
        buttons_card.grid(row=3, column=0, columnspan=2, pady=(15, 0), sticky=(tk.W, tk.E))

        buttons_content = buttons_card.content_frame
        buttons_content.columnconfigure((0, 1, 2, 3, 4), weight=1)

        # الصف الأول من الأزرار
        ModernButton(buttons_content, text="➕ إضافة طالب", command=self.add_student,
                    style="Success.TButton").grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="✏️ تحديث البيانات", command=self.update_student,
                    style="Primary.TButton").grid(row=0, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="🗑️ حذف الطالب", command=self.delete_student,
                    style="Danger.TButton").grid(row=0, column=2, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="🧹 مسح الحقول", command=self.clear_fields,
                    style="TButton").grid(row=0, column=3, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="🔄 تحديث القائمة", command=self.load_students,
                    style="TButton").grid(row=0, column=4, padx=5, pady=5, sticky=(tk.W, tk.E))

        # الصف الثاني من الأزرار (عمليات إضافية)
        ModernButton(buttons_content, text="📊 إحصائيات الطالب", command=self.show_student_stats,
                    style="Warning.TButton").grid(row=1, column=0, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="📄 تصدير البيانات", command=self.export_students,
                    style="TButton").grid(row=1, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="📥 استيراد البيانات", command=self.import_students,
                    style="TButton").grid(row=1, column=2, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="🖨️ طباعة التفاصيل", command=self.print_student_info,
                    style="TButton").grid(row=1, column=3, padx=5, pady=5, sticky=(tk.W, tk.E))

        ModernButton(buttons_content, text="❌ إغلاق النافذة", command=self.on_closing,
                    style="TButton").grid(row=1, column=4, padx=5, pady=5, sticky=(tk.W, tk.E))

        # زر ملء بيانات تجريبية (للاختبار)
        ModernButton(buttons_content, text="🧪 بيانات تجريبية", command=self.fill_test_data,
                    style="TButton").grid(row=2, column=0, columnspan=2, padx=5, pady=5, sticky=(tk.W, tk.E))

    def create_context_menu(self):
        """إنشاء قائمة السياق (Right-click menu)"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="عرض التفاصيل", command=self.show_student_details)
        self.context_menu.add_command(label="تعديل", command=self.edit_student)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="حذف", command=self.delete_student)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="عرض الغيابات", command=self.show_student_absences)

        # ربط القائمة بالجدول
        self.students_table.tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def clear_search(self):
        """مسح البحث"""
        self.search_entry.set("")
        self.grade_combo.set("الكل")
        self.section_combo.set("الكل")
        self.load_students()

    def show_student_stats(self):
        """عرض إحصائيات الطالب"""
        selected = self.students_table.get_selected()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى تحديد طالب أولاً")
            return

        # هنا يمكن إضافة نافذة إحصائيات مفصلة
        messagebox.showinfo("إحصائيات", f"إحصائيات الطالب: {selected[1]}\n(قيد التطوير)")

    def export_students(self):
        """تصدير بيانات الطلاب"""
        messagebox.showinfo("تصدير", "ميزة تصدير البيانات قيد التطوير")

    def import_students(self):
        """استيراد بيانات الطلاب"""
        messagebox.showinfo("استيراد", "ميزة استيراد البيانات قيد التطوير")

    def print_student_info(self):
        """طباعة معلومات الطالب"""
        selected = self.students_table.get_selected()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى تحديد طالب أولاً")
            return

        messagebox.showinfo("طباعة", f"طباعة معلومات الطالب: {selected[1]}\n(قيد التطوير)")

    def show_student_details(self):
        """عرض تفاصيل الطالب"""
        selected = self.students_table.get_selected()
        if selected:
            self.on_student_select(None)

    def edit_student(self):
        """تعديل الطالب"""
        self.show_student_details()

    def show_student_absences(self):
        """عرض غيابات الطالب"""
        selected = self.students_table.get_selected()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى تحديد طالب أولاً")
            return

        messagebox.showinfo("غيابات", f"غيابات الطالب: {selected[1]}\n(قيد التطوير)")

    def fill_test_data(self):
        """ملء بيانات تجريبية للاختبار"""
        try:
            import random

            # بيانات تجريبية
            test_names = [
                "أحمد محمد علي",
                "فاطمة سالم أحمد",
                "عبدالله يوسف محمد",
                "مريم عبدالرحمن",
                "خالد عبدالعزيز",
                "نورا أحمد سالم"
            ]

            # اختيار اسم عشوائي
            name = random.choice(test_names)
            student_id = str(random.randint(10000, 99999))

            # ملء الحقول
            self.student_entries['student_id'].set(student_id)
            self.student_entries['name'].set(name)
            self.student_entries['grade'].set("الثاني متوسط")
            self.student_entries['class_section'].set("أ")
            self.student_entries['phone'].set("0501234567")
            self.student_entries['parent_phone'].set("0507654321")

            # ملء العنوان
            self.address_text.delete('1.0', tk.END)
            self.address_text.insert('1.0', "الرياض، حي النخيل، شارع الملك فهد")

            print(f"✅ تم ملء البيانات التجريبية: {name} - {student_id}")
            messagebox.showinfo("تم", f"تم ملء البيانات التجريبية\nالاسم: {name}\nرقم الطالب: {student_id}")

        except Exception as e:
            print(f"❌ خطأ في ملء البيانات التجريبية: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في ملء البيانات التجريبية: {str(e)}")

    # ==================== دوال العمليات ====================

    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            loading = LoadingDialog(self.window, "جاري تحميل قائمة الطلاب...")

            # الحصول على الطلاب من قاعدة البيانات
            students = self.db_manager.get_students()

            # تحضير البيانات للجدول
            students_data = []
            for student in students:
                grade_name = Config.GRADES.get(student['grade'], str(student['grade']))
                students_data.append([
                    student['student_id'],
                    student['name'],
                    grade_name,
                    student['class_section'] or '',
                    student['phone'] or ''
                ])

            # تحديث الجدول
            self.students_table.set_data(students_data)

            # تحديث الإحصائيات
            self.total_students_label.config(text=f"إجمالي الطلاب: {len(students)}")

            loading.close()

        except Exception as e:
            if 'loading' in locals():
                loading.close()
            messagebox.showerror("خطأ", f"خطأ في تحميل قائمة الطلاب: {str(e)}")

    def search_students(self):
        """البحث عن الطلاب"""
        try:
            search_term = self.search_entry.get().strip()
            grade_filter = self.grade_combo.get()
            section_filter = self.section_combo.get()

            # تحديد الصف للبحث
            grade = None
            if grade_filter != 'الكل':
                for grade_num, grade_name in Config.GRADES.items():
                    if grade_name == grade_filter:
                        grade = grade_num
                        break

            # الحصول على النتائج
            students = self.db_manager.get_students(grade=grade, search_term=search_term)

            # تطبيق تصفية الشعبة
            if section_filter != 'الكل':
                students = [s for s in students if s.get('class_section') == section_filter]

            # تحضير البيانات للجدول
            students_data = []
            for student in students:
                grade_name = Config.GRADES.get(student['grade'], str(student['grade']))
                students_data.append([
                    student['student_id'],
                    student['name'],
                    grade_name,
                    student['class_section'] or '',
                    student['phone'] or ''
                ])

            # تحديث الجدول
            self.students_table.set_data(students_data)

            # تحديث الإحصائيات
            self.total_students_label.config(text=f"نتائج البحث: {len(students)} طالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")

    def on_search_change(self, *args):
        """التعامل مع تغيير نص البحث"""
        # تأخير البحث لتحسين الأداء
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.search_students)

    def on_student_select(self, event):
        """التعامل مع تحديد طالب من القائمة"""
        selected = self.students_table.get_selected()
        if selected:
            student_id = selected[0]  # رقم الطالب
            self.load_student_details_by_student_id(student_id)

    def load_student_details_by_student_id(self, student_id):
        """تحميل تفاصيل طالب معين بواسطة رقم الطالب"""
        try:
            student = self.db_manager.get_student(student_id)
            if student:
                self.current_student_id = student['id']

                # تحديث الحقول
                self.student_entries['student_id'].set(student['student_id'])
                self.student_entries['name'].set(student['name'])
                self.student_entries['grade'].set(Config.GRADES.get(student['grade'], ''))
                self.student_entries['class_section'].set(student['class_section'] or '')
                self.student_entries['phone'].set(student['phone'] or '')
                self.student_entries['parent_phone'].set(student['parent_phone'] or '')

                # تحديث حقل العنوان
                self.address_text.delete('1.0', tk.END)
                self.address_text.insert('1.0', student['address'] or '')

                # تحديث معلومات الطالب
                self.student_info_label.config(text=f"الطالب المحدد: {student['name']} - {student['student_id']}")
                self.selected_student_label.config(text=f"محدد: {student['name']}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الطالب: {str(e)}")

    def validate_student_data(self):
        """التحقق من صحة بيانات الطالب"""
        errors = []

        try:
            # التحقق من رقم الطالب
            student_id = self.student_entries['student_id'].get().strip()
            if not student_id:
                errors.append("رقم الطالب مطلوب")
            elif not student_id.isdigit():
                errors.append("رقم الطالب يجب أن يحتوي على أرقام فقط")

            # التحقق من اسم الطالب
            name = self.student_entries['name'].get().strip()
            if not name:
                errors.append("اسم الطالب مطلوب")
            elif len(name) < 2:
                errors.append("اسم الطالب يجب أن يكون أكثر من حرفين")

            # التحقق من الصف
            grade = self.student_entries['grade'].get().strip()
            if not grade:
                errors.append("الصف الدراسي مطلوب")

            # التحقق من الشعبة
            section = self.student_entries['class_section'].get().strip()
            if not section:
                errors.append("الشعبة مطلوبة")

            # التحقق من رقم الهاتف (اختياري)
            phone = self.student_entries['phone'].get().strip()
            if phone and not phone.startswith('05') and not phone.startswith('+966'):
                errors.append("رقم الهاتف يجب أن يبدأ بـ 05 أو +966")

        except Exception as e:
            errors.append(f"خطأ في التحقق من البيانات: {str(e)}")

        return errors

    def get_student_data(self):
        """الحصول على بيانات الطالب من النموذج"""
        try:
            # تحويل اسم الصف إلى رقم
            grade_name = self.student_entries['grade'].get().strip()
            grade = None

            if grade_name:
                for grade_num, name in Config.GRADES.items():
                    if name == grade_name:
                        grade = grade_num
                        break

                # إذا لم نجد الصف، نستخدم الصف الأول كافتراضي
                if grade is None and grade_name:
                    print(f"⚠️ لم يتم العثور على الصف: {grade_name}")
                    grade = 1  # الصف الأول كافتراضي

            # الحصول على البيانات
            student_data = {
                'student_id': self.student_entries['student_id'].get().strip(),
                'name': self.student_entries['name'].get().strip(),
                'grade': grade,
                'class_section': self.student_entries['class_section'].get().strip(),
                'phone': self.student_entries['phone'].get().strip() or None,
                'parent_phone': self.student_entries['parent_phone'].get().strip() or None,
                'address': self.address_text.get('1.0', tk.END).strip() or None
            }

            # طباعة البيانات للتشخيص
            print(f"📊 بيانات الطالب المجمعة:")
            for key, value in student_data.items():
                print(f"   {key}: {value}")

            return student_data

        except Exception as e:
            print(f"❌ خطأ في جمع بيانات الطالب: {str(e)}")
            raise

    def add_student(self):
        """إضافة طالب جديد"""
        try:
            print("🔍 بدء عملية إضافة طالب...")

            # التحقق من وجود الحقول
            if not hasattr(self, 'student_entries'):
                messagebox.showerror("خطأ", "لم يتم تهيئة حقول البيانات بشكل صحيح")
                return

            # التحقق من صحة البيانات
            print("📝 التحقق من صحة البيانات...")
            errors = self.validate_student_data()
            if errors:
                error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
                error_message += "\n\n💡 نصيحة: استخدم زر 'بيانات تجريبية' لملء الحقول تلقائياً"
                messagebox.showerror("خطأ في البيانات", error_message)
                return

            # الحصول على البيانات
            print("📊 الحصول على بيانات الطالب...")
            student_data = self.get_student_data()

            # طباعة البيانات للتشخيص
            print(f"البيانات المرسلة: {student_data}")

            # التحقق من عدم وجود طالب بنفس الرقم
            existing_students = self.db_manager.get_students()
            for student in existing_students:
                if student['student_id'] == student_data['student_id']:
                    messagebox.showerror("خطأ", f"يوجد طالب آخر بنفس الرقم: {student_data['student_id']}")
                    return

            # إضافة الطالب
            print("💾 إضافة الطالب إلى قاعدة البيانات...")
            student_id = self.db_manager.add_student(student_data)

            if student_id:
                messagebox.showinfo("نجح", f"تم إضافة الطالب بنجاح\nرقم الطالب: {student_data['student_id']}\nالاسم: {student_data['name']}")

                # تحديث القائمة ومسح الحقول
                print("🔄 تحديث القائمة...")
                self.load_students()
                self.clear_fields()
                print("✅ تمت العملية بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الطالب إلى قاعدة البيانات")

        except Exception as e:
            print(f"❌ خطأ في إضافة الطالب: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"خطأ في إضافة الطالب:\n{str(e)}")

    def update_student(self):
        """تحديث بيانات طالب"""
        if not hasattr(self, 'current_student_id') or not self.current_student_id:
            messagebox.showwarning("تحذير", "يرجى تحديد طالب من القائمة أولاً")
            return

        try:
            # التحقق من صحة البيانات
            errors = self.validate_student_data()
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return

            # الحصول على البيانات
            student_data = self.get_student_data()

            # تحديث الطالب
            success = self.db_manager.update_student(self.current_student_id, student_data)

            if success:
                messagebox.showinfo("نجح", "تم تحديث بيانات الطالب بنجاح")
                self.load_students()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث بيانات الطالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الطالب: {str(e)}")

    def delete_student(self):
        """حذف طالب"""
        if not hasattr(self, 'current_student_id') or not self.current_student_id:
            messagebox.showwarning("تحذير", "يرجى تحديد طالب من القائمة أولاً")
            return

        # تأكيد الحذف
        student_name = self.student_vars['name'].get()
        if not messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف الطالب '{student_name}'؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه."):
            return

        try:
            # حذف الطالب
            success = self.db_manager.delete_student(self.current_student_id)

            if success:
                messagebox.showinfo("نجح", "تم حذف الطالب بنجاح")
                self.load_students()
                self.clear_fields()
            else:
                messagebox.showerror("خطأ", "فشل في حذف الطالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف الطالب: {str(e)}")

    def clear_fields(self):
        """مسح جميع الحقول"""
        self.current_student_id = None

        for entry in self.student_entries.values():
            entry.set('')

        self.address_text.delete('1.0', tk.END)

        # تحديث المعلومات
        self.student_info_label.config(text="اختر طالباً لعرض تفاصيله")
        self.selected_student_label.config(text="لم يتم تحديد طالب")

        # إلغاء التحديد من القائمة
        for item in self.students_table.tree.selection():
            self.students_table.tree.selection_remove(item)

    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        self.window.destroy()

# دالة لفتح نافذة إدارة الطلاب
def open_students_management(parent=None):
    """فتح نافذة إدارة الطلاب"""
    return StudentsManagementWindow(parent)
