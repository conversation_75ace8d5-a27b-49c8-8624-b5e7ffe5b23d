#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دعم الاتجاه من اليمين إلى اليسار (RTL)
يحتوي على دوال وإعدادات لتحسين دعم اللغة العربية
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class RTLSupport:
    """فئة دعم الاتجاه من اليمين إلى اليسار"""
    
    @staticmethod
    def configure_widget_rtl(widget, widget_type=None):
        """تكوين عنصر واجهة لدعم RTL"""
        try:
            # تحديد نوع العنصر إذا لم يتم تمريره
            if widget_type is None:
                widget_type = widget.__class__.__name__
            
            # إعدادات عامة لجميع العناصر
            if hasattr(widget, 'configure'):
                # محاذاة النص إلى اليمين للنصوص العربية
                if widget_type in ['Label', 'Button', 'Entry', 'Text', 'Listbox']:
                    try:
                        widget.configure(justify='right')
                    except:
                        pass
                
                # تعيين اتجاه النص للعناصر التي تدعمه
                if widget_type in ['Entry', 'Text']:
                    try:
                        # محاولة تعيين اتجاه النص (قد لا يكون مدعوماً في جميع الإصدارات)
                        widget.configure(justify='right')
                    except:
                        pass
            
            # إعدادات خاصة حسب نوع العنصر
            if widget_type == 'Treeview':
                RTLSupport._configure_treeview_rtl(widget)
            elif widget_type == 'Frame':
                RTLSupport._configure_frame_rtl(widget)
            elif widget_type == 'LabelFrame':
                RTLSupport._configure_labelframe_rtl(widget)
            elif widget_type == 'Text':
                RTLSupport._configure_text_rtl(widget)
            elif widget_type == 'Entry':
                RTLSupport._configure_entry_rtl(widget)
            
        except Exception as e:
            print(f"تحذير: لا يمكن تكوين RTL للعنصر {widget_type}: {str(e)}")
    
    @staticmethod
    def _configure_treeview_rtl(treeview):
        """تكوين Treeview لدعم RTL"""
        try:
            # محاذاة الأعمدة إلى اليمين
            for col in treeview['columns']:
                treeview.column(col, anchor='e')  # 'e' = east = يمين
                treeview.heading(col, anchor='e')
        except Exception as e:
            print(f"تحذير: لا يمكن تكوين Treeview RTL: {str(e)}")
    
    @staticmethod
    def _configure_frame_rtl(frame):
        """تكوين Frame لدعم RTL"""
        try:
            # لا توجد إعدادات خاصة للـ Frame حالياً
            pass
        except Exception as e:
            print(f"تحذير: لا يمكن تكوين Frame RTL: {str(e)}")
    
    @staticmethod
    def _configure_labelframe_rtl(labelframe):
        """تكوين LabelFrame لدعم RTL"""
        try:
            # محاذاة النص في LabelFrame
            labelframe.configure(labelanchor='ne')  # شمال شرق (يمين أعلى)
        except Exception as e:
            print(f"تحذير: لا يمكن تكوين LabelFrame RTL: {str(e)}")
    
    @staticmethod
    def _configure_text_rtl(text_widget):
        """تكوين Text widget لدعم RTL"""
        try:
            # تعيين محاذاة النص إلى اليمين
            text_widget.configure(justify='right')
            
            # إنشاء tag للنص العربي
            text_widget.tag_configure("rtl", justify='right')
            
            # ربط الأحداث لتطبيق RTL تلقائياً
            def on_key_press(event):
                try:
                    # تطبيق tag RTL على النص الجديد
                    text_widget.tag_add("rtl", "1.0", "end")
                except:
                    pass
            
            text_widget.bind('<KeyPress>', on_key_press)
            
        except Exception as e:
            print(f"تحذير: لا يمكن تكوين Text RTL: {str(e)}")
    
    @staticmethod
    def _configure_entry_rtl(entry_widget):
        """تكوين Entry widget لدعم RTL"""
        try:
            # محاذاة النص إلى اليمين
            entry_widget.configure(justify='right')
        except Exception as e:
            print(f"تحذير: لا يمكن تكوين Entry RTL: {str(e)}")
    
    @staticmethod
    def setup_rtl_grid(parent, widgets_info):
        """إعداد شبكة RTL للعناصر"""
        """
        widgets_info: قائمة من المعاجم تحتوي على معلومات العناصر
        مثال: [{'widget': widget1, 'row': 0, 'column': 0, 'sticky': 'e'}, ...]
        """
        try:
            for info in widgets_info:
                widget = info['widget']
                row = info.get('row', 0)
                column = info.get('column', 0)
                sticky = info.get('sticky', 'e')  # افتراضي إلى اليمين
                padx = info.get('padx', 5)
                pady = info.get('pady', 5)
                columnspan = info.get('columnspan', 1)
                rowspan = info.get('rowspan', 1)
                
                # تعديل sticky لدعم RTL
                if 'w' in sticky:
                    sticky = sticky.replace('w', 'e')
                elif 'e' not in sticky and 'w' not in sticky:
                    sticky += 'e'
                
                widget.grid(
                    row=row, 
                    column=column, 
                    sticky=sticky,
                    padx=padx,
                    pady=pady,
                    columnspan=columnspan,
                    rowspan=rowspan
                )
                
                # تكوين العنصر لدعم RTL
                RTLSupport.configure_widget_rtl(widget)
                
        except Exception as e:
            print(f"تحذير: لا يمكن إعداد RTL grid: {str(e)}")
    
    @staticmethod
    def create_rtl_label(parent, text, **kwargs):
        """إنشاء Label مع دعم RTL"""
        label = ttk.Label(parent, text=text, **kwargs)
        RTLSupport.configure_widget_rtl(label, 'Label')
        return label
    
    @staticmethod
    def create_rtl_button(parent, text, command=None, **kwargs):
        """إنشاء Button مع دعم RTL"""
        button = ttk.Button(parent, text=text, command=command, **kwargs)
        RTLSupport.configure_widget_rtl(button, 'Button')
        return button
    
    @staticmethod
    def create_rtl_entry(parent, textvariable=None, **kwargs):
        """إنشاء Entry مع دعم RTL"""
        entry = ttk.Entry(parent, textvariable=textvariable, **kwargs)
        RTLSupport.configure_widget_rtl(entry, 'Entry')
        return entry
    
    @staticmethod
    def create_rtl_text(parent, **kwargs):
        """إنشاء Text widget مع دعم RTL"""
        text_widget = tk.Text(parent, **kwargs)
        RTLSupport.configure_widget_rtl(text_widget, 'Text')
        return text_widget
    
    @staticmethod
    def create_rtl_treeview(parent, columns, **kwargs):
        """إنشاء Treeview مع دعم RTL"""
        treeview = ttk.Treeview(parent, columns=columns, **kwargs)
        RTLSupport.configure_widget_rtl(treeview, 'Treeview')
        return treeview
    
    @staticmethod
    def create_rtl_labelframe(parent, text, **kwargs):
        """إنشاء LabelFrame مع دعم RTL"""
        labelframe = ttk.LabelFrame(parent, text=text, **kwargs)
        RTLSupport.configure_widget_rtl(labelframe, 'LabelFrame')
        return labelframe
    
    @staticmethod
    def apply_rtl_to_window(window):
        """تطبيق إعدادات RTL على نافذة كاملة"""
        try:
            # تطبيق RTL على جميع العناصر الفرعية
            def apply_to_children(widget):
                try:
                    RTLSupport.configure_widget_rtl(widget)
                    for child in widget.winfo_children():
                        apply_to_children(child)
                except:
                    pass
            
            apply_to_children(window)
            
        except Exception as e:
            print(f"تحذير: لا يمكن تطبيق RTL على النافذة: {str(e)}")
    
    @staticmethod
    def reverse_grid_columns(parent, max_columns):
        """عكس ترتيب الأعمدة في الشبكة لدعم RTL"""
        try:
            children = parent.winfo_children()
            for child in children:
                grid_info = child.grid_info()
                if grid_info:
                    current_column = grid_info.get('column', 0)
                    new_column = max_columns - 1 - current_column
                    
                    # إعادة ترتيب العنصر
                    child.grid_configure(column=new_column)
                    
        except Exception as e:
            print(f"تحذير: لا يمكن عكس ترتيب الأعمدة: {str(e)}")
    
    @staticmethod
    def get_arabic_font():
        """الحصول على خط مناسب للعربية"""
        # قائمة الخطوط المفضلة للعربية
        arabic_fonts = [
            'Arial Unicode MS',
            'Tahoma',
            'Arial',
            'Segoe UI',
            'Microsoft Sans Serif',
            'DejaVu Sans'
        ]
        
        # إرجاع أول خط متوفر
        import tkinter.font as tkFont
        available_fonts = tkFont.families()
        
        for font in arabic_fonts:
            if font in available_fonts:
                return font
        
        # إرجاع الخط الافتراضي إذا لم يوجد خط عربي
        return 'TkDefaultFont'

# إنشاء مثيل عام للاستخدام
rtl_support = RTLSupport()
