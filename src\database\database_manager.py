#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
يدير جميع العمليات المتعلقة بقاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple
from src.utils.config import Config

class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        Config.ensure_directories()
        self.db_path = Config.DATABASE_PATH
        self.connection = None
    
    def get_connection(self) -> sqlite3.Connection:
        """الحصول على اتصال بقاعدة البيانات"""
        if self.connection is None:
            self.connection = sqlite3.connect(
                self.db_path,
                timeout=Config.DB_TIMEOUT,
                check_same_thread=False
            )
            self.connection.row_factory = sqlite3.Row
            # تفعيل دعم المفاتيح الخارجية
            self.connection.execute("PRAGMA foreign_keys = ON")
        return self.connection
    
    def close_connection(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # جدول الطلاب
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS students (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    grade INTEGER NOT NULL CHECK (grade IN (1, 2, 3)),
                    class_section TEXT,
                    phone TEXT,
                    parent_phone TEXT,
                    address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            
            # جدول السنوات الدراسية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS academic_years (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    year_name TEXT UNIQUE NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    is_current BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول الفصول الدراسية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS semesters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    academic_year_id INTEGER NOT NULL,
                    semester_number INTEGER NOT NULL CHECK (semester_number IN (1, 2, 3)),
                    name TEXT NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    is_current BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (academic_year_id) REFERENCES academic_years (id),
                    UNIQUE (academic_year_id, semester_number)
                )
            """)
            
            # جدول الغياب
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS attendance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    semester_id INTEGER NOT NULL,
                    absence_date DATE NOT NULL,
                    absence_type TEXT NOT NULL CHECK (absence_type IN ('excused', 'unexcused', 'permission')),
                    reason TEXT,
                    notes TEXT,
                    created_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id),
                    FOREIGN KEY (semester_id) REFERENCES semesters (id),
                    UNIQUE (student_id, absence_date)
                )
            """)
            
            # جدول إعدادات النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء الفهارس لتحسين الأداء
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_students_student_id ON students (student_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_students_grade ON students (grade)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_attendance_student_date ON attendance (student_id, absence_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_attendance_semester ON attendance (semester_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance (absence_date)")
            
            # إدراج البيانات الافتراضية
            self._insert_default_data(cursor)
            
            conn.commit()
            print("تم تهيئة قاعدة البيانات بنجاح")
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
    
    def _insert_default_data(self, cursor):
        """إدراج البيانات الافتراضية"""
        # إدراج السنة الدراسية الحالية إذا لم تكن موجودة
        current_year = Config.get_academic_year()
        cursor.execute("""
            INSERT OR IGNORE INTO academic_years (year_name, start_date, end_date, is_current)
            VALUES (?, ?, ?, 1)
        """, (current_year, f"{current_year.split('-')[0]}-09-01", f"{current_year.split('-')[1]}-06-30"))
        
        # الحصول على معرف السنة الدراسية
        cursor.execute("SELECT id FROM academic_years WHERE year_name = ?", (current_year,))
        year_id = cursor.fetchone()[0]
        
        # إدراج الفصول الدراسية
        semesters_data = [
            (year_id, 1, "الفصل الدراسي الأول", f"{current_year.split('-')[0]}-09-01", f"{current_year.split('-')[0]}-12-31"),
            (year_id, 2, "الفصل الدراسي الثاني", f"{current_year.split('-')[1]}-01-01", f"{current_year.split('-')[1]}-04-30"),
            (year_id, 3, "الفصل الدراسي الثالث", f"{current_year.split('-')[1]}-05-01", f"{current_year.split('-')[1]}-06-30")
        ]
        
        for semester_data in semesters_data:
            cursor.execute("""
                INSERT OR IGNORE INTO semesters 
                (academic_year_id, semester_number, name, start_date, end_date, is_current)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (*semester_data, semester_data[1] == Config.get_current_semester()))
        
        # إدراج إعدادات النظام الافتراضية
        default_settings = [
            ("max_absence_days", str(Config.MAX_ABSENCE_DAYS), "الحد الأقصى لأيام الغياب المسموح بها"),
            ("school_name", "مدرسة المتوسطة", "اسم المدرسة"),
            ("academic_year", current_year, "السنة الدراسية الحالية"),
            ("current_semester", str(Config.get_current_semester()), "الفصل الدراسي الحالي")
        ]
        
        for setting in default_settings:
            cursor.execute("""
                INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, setting)

    # ==================== دوال إدارة الطلاب ====================

    def add_student(self, student_data: Dict) -> int:
        """إضافة طالب جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO students (student_id, name, grade, class_section, phone, parent_phone, address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                student_data['student_id'],
                student_data['name'],
                student_data['grade'],
                student_data.get('class_section', ''),
                student_data.get('phone', ''),
                student_data.get('parent_phone', ''),
                student_data.get('address', '')
            ))

            student_id = cursor.lastrowid
            conn.commit()
            return student_id

        except sqlite3.IntegrityError:
            raise Exception("رقم الطالب موجود مسبقاً")
        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في إضافة الطالب: {str(e)}")

    def update_student(self, student_id: int, student_data: Dict) -> bool:
        """تحديث بيانات طالب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE students
                SET name = ?, grade = ?, class_section = ?, phone = ?,
                    parent_phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                student_data['name'],
                student_data['grade'],
                student_data.get('class_section', ''),
                student_data.get('phone', ''),
                student_data.get('parent_phone', ''),
                student_data.get('address', ''),
                student_id
            ))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في تحديث بيانات الطالب: {str(e)}")

    def delete_student(self, student_id: int) -> bool:
        """حذف طالب (إلغاء تفعيل)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE students
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (student_id,))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في حذف الطالب: {str(e)}")

    def get_student(self, student_id: int) -> Optional[Dict]:
        """الحصول على بيانات طالب بواسطة المعرف الداخلي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM students WHERE id = ? AND is_active = 1
        """, (student_id,))

        row = cursor.fetchone()
        return dict(row) if row else None

    def get_student_by_student_id(self, student_id: str) -> Optional[Dict]:
        """الحصول على بيانات طالب بواسطة رقم الطالب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM students WHERE student_id = ? AND is_active = 1
        """, (student_id,))

        row = cursor.fetchone()
        return dict(row) if row else None

    def get_students(self, grade: Optional[int] = None, search_term: str = "") -> List[Dict]:
        """الحصول على قائمة الطلاب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = "SELECT * FROM students WHERE is_active = 1"
        params = []

        if grade:
            query += " AND grade = ?"
            params.append(grade)

        if search_term:
            query += " AND (name LIKE ? OR student_id LIKE ?)"
            params.extend([f"%{search_term}%", f"%{search_term}%"])

        query += " ORDER BY grade, name"

        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]

    # ==================== دوال إدارة الغياب ====================

    def add_absence(self, absence_data: Dict) -> int:
        """تسجيل غياب طالب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على الفصل الدراسي الحالي
            current_semester = self.get_current_semester_id()

            cursor.execute("""
                INSERT INTO attendance (student_id, semester_id, absence_date, absence_type, reason, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                absence_data['student_id'],
                current_semester,
                absence_data['absence_date'],
                absence_data['absence_type'],
                absence_data.get('reason', ''),
                absence_data.get('notes', ''),
                absence_data.get('created_by', 'النظام')
            ))

            absence_id = cursor.lastrowid
            conn.commit()
            return absence_id

        except sqlite3.IntegrityError:
            raise Exception("تم تسجيل غياب لهذا الطالب في هذا التاريخ مسبقاً")
        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في تسجيل الغياب: {str(e)}")

    def update_absence(self, absence_id: int, absence_data: Dict) -> bool:
        """تحديث سجل غياب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE attendance
                SET absence_type = ?, reason = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                absence_data['absence_type'],
                absence_data.get('reason', ''),
                absence_data.get('notes', ''),
                absence_id
            ))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في تحديث سجل الغياب: {str(e)}")

    def delete_absence(self, absence_id: int) -> bool:
        """حذف سجل غياب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("DELETE FROM attendance WHERE id = ?", (absence_id,))
            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            raise Exception(f"خطأ في حذف سجل الغياب: {str(e)}")

    def get_student_absences(self, student_id: int, semester_id: Optional[int] = None) -> List[Dict]:
        """الحصول على سجلات غياب طالب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = """
            SELECT a.*, s.name as student_name, sem.name as semester_name
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            JOIN semesters sem ON a.semester_id = sem.id
            WHERE a.student_id = ?
        """
        params = [student_id]

        if semester_id:
            query += " AND a.semester_id = ?"
            params.append(semester_id)

        query += " ORDER BY a.absence_date DESC"

        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]

    def get_daily_absences(self, absence_date: str, grade: Optional[int] = None) -> List[Dict]:
        """الحصول على غيابات يوم معين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = """
            SELECT a.*, s.name as student_name, s.student_id, s.grade, s.class_section
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.absence_date = ?
        """
        params = [absence_date]

        if grade:
            query += " AND s.grade = ?"
            params.append(grade)

        query += " ORDER BY s.grade, s.name"

        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]

    # ==================== دوال مساعدة ====================

    def get_current_semester_id(self) -> int:
        """الحصول على معرف الفصل الدراسي الحالي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id FROM semesters
            WHERE is_current = 1
            ORDER BY id DESC
            LIMIT 1
        """)

        row = cursor.fetchone()
        if row:
            return row[0]
        else:
            # إذا لم يوجد فصل حالي، نأخذ الأحدث
            cursor.execute("SELECT id FROM semesters ORDER BY id DESC LIMIT 1")
            row = cursor.fetchone()
            return row[0] if row else 1

    def get_semesters(self) -> List[Dict]:
        """الحصول على قائمة الفصول الدراسية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.*, ay.year_name
            FROM semesters s
            JOIN academic_years ay ON s.academic_year_id = ay.id
            ORDER BY ay.year_name DESC, s.semester_number
        """)

        return [dict(row) for row in cursor.fetchall()]

    # ==================== دوال التقارير والإحصائيات ====================

    def get_absence_statistics(self, semester_id: Optional[int] = None, grade: Optional[int] = None) -> Dict:
        """الحصول على إحصائيات الغياب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # بناء الاستعلام الأساسي
        base_query = """
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE s.is_active = 1
        """
        params = []

        if semester_id:
            base_query += " AND a.semester_id = ?"
            params.append(semester_id)

        if grade:
            base_query += " AND s.grade = ?"
            params.append(grade)

        # إجمالي الغيابات
        cursor.execute(f"SELECT COUNT(*) {base_query}", params)
        total_absences = cursor.fetchone()[0]

        # الغيابات بعذر
        cursor.execute(f"SELECT COUNT(*) {base_query} AND a.absence_type = 'excused'", params)
        excused_absences = cursor.fetchone()[0]

        # الغيابات بدون عذر
        cursor.execute(f"SELECT COUNT(*) {base_query} AND a.absence_type = 'unexcused'", params)
        unexcused_absences = cursor.fetchone()[0]

        # الاستئذانات
        cursor.execute(f"SELECT COUNT(*) {base_query} AND a.absence_type = 'permission'", params)
        permissions = cursor.fetchone()[0]

        # عدد الطلاب المتغيبين
        cursor.execute(f"SELECT COUNT(DISTINCT a.student_id) {base_query}", params)
        absent_students = cursor.fetchone()[0]

        return {
            'total_absences': total_absences,
            'excused_absences': excused_absences,
            'unexcused_absences': unexcused_absences,
            'permissions': permissions,
            'absent_students': absent_students
        }

    def get_student_absence_summary(self, student_id: int, semester_id: Optional[int] = None) -> Dict:
        """الحصول على ملخص غيابات طالب"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = "SELECT absence_type, COUNT(*) as count FROM attendance WHERE student_id = ?"
        params = [student_id]

        if semester_id:
            query += " AND semester_id = ?"
            params.append(semester_id)

        query += " GROUP BY absence_type"

        cursor.execute(query, params)
        results = cursor.fetchall()

        summary = {
            'excused': 0,
            'unexcused': 0,
            'permission': 0,
            'total': 0
        }

        for row in results:
            summary[row[0]] = row[1]
            summary['total'] += row[1]

        return summary

    def get_top_absent_students(self, limit: int = 10, semester_id: Optional[int] = None) -> List[Dict]:
        """الحصول على الطلاب الأكثر غياباً"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = """
            SELECT s.id, s.name, s.student_id, s.grade, s.class_section,
                   COUNT(a.id) as total_absences,
                   SUM(CASE WHEN a.absence_type = 'excused' THEN 1 ELSE 0 END) as excused,
                   SUM(CASE WHEN a.absence_type = 'unexcused' THEN 1 ELSE 0 END) as unexcused,
                   SUM(CASE WHEN a.absence_type = 'permission' THEN 1 ELSE 0 END) as permissions
            FROM students s
            LEFT JOIN attendance a ON s.id = a.student_id
            WHERE s.is_active = 1
        """
        params = []

        if semester_id:
            query += " AND (a.semester_id = ? OR a.semester_id IS NULL)"
            params.append(semester_id)

        query += """
            GROUP BY s.id, s.name, s.student_id, s.grade, s.class_section
            HAVING total_absences > 0
            ORDER BY total_absences DESC
            LIMIT ?
        """
        params.append(limit)

        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]

    def get_absence_trends(self, days: int = 30) -> List[Dict]:
        """الحصول على اتجاهات الغياب خلال فترة معينة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT absence_date, absence_type, COUNT(*) as count
            FROM attendance
            WHERE absence_date >= date('now', '-{} days')
            GROUP BY absence_date, absence_type
            ORDER BY absence_date DESC
        """.format(days))

        return [dict(row) for row in cursor.fetchall()]

    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        self.close_connection()
