# برنامج تسجيل غياب الطلاب الذكي

## نظرة عامة

برنامج شامل لإدارة غياب طلاب المرحلة المتوسطة مع دعم الذكاء الصناعي لتحليل أنماط الغياب والتنبؤ بالغياب المستقبلي.

## الميزات الرئيسية

### 🎯 إدارة الطلاب
- إضافة وتعديل وحذف بيانات الطلاب
- تصنيف الطلاب حسب الصفوف والشعب
- البحث والتصفية المتقدمة
- حفظ معلومات الاتصال وأولياء الأمور

### 📝 تسجيل الغياب
- تسجيل الغياب اليومي بسهولة
- تصنيف أنواع الغياب (بعذر/بدون عذر/استئذان)
- إضافة الأسباب والملاحظات
- تعديل وحذف سجلات الغياب

### 📊 التقارير والإحصائيات
- تقارير يومية وأسبوعية وشهرية وفصلية
- إحصائيات مفصلة حسب الطالب والصف
- تصفية متقدمة للتقارير
- رسوم بيانية وتحليلات بصرية

### 🤖 الذكاء الصناعي
- تحليل أنماط الغياب
- التنبؤ بالغياب المستقبلي
- تحديد الطلاب المعرضين للخطر
- توصيات ذكية لتحسين الحضور

### 📄 الطباعة والتصدير
- تصدير التقارير بصيغة PDF
- تصدير البيانات إلى Excel
- طباعة التقارير مباشرة
- تصدير CSV للتحليل الخارجي

### 🗄️ إدارة البيانات
- قاعدة بيانات SQLite محلية
- نسخ احتياطي تلقائي
- استيراد وتصدير البيانات
- حماية البيانات والخصوصية

## متطلبات النظام

### المتطلبات الأساسية
- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10/11, macOS, Linux
- ذاكرة: 4 GB RAM (الحد الأدنى)
- مساحة القرص: 500 MB

### المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

المكتبات الأساسية:
- `tkinter` (مدمجة مع Python)
- `sqlite3` (مدمجة مع Python)
- `pandas` - للتعامل مع البيانات
- `matplotlib` - للرسوم البيانية
- `scikit-learn` - للذكاء الصناعي
- `reportlab` - لإنشاء ملفات PDF
- `openpyxl` - لملفات Excel
- `tkcalendar` - لاختيار التواريخ

## 🎨 **الإصدار الجديد - واجهات محسنة ومتطورة**

### ✨ **الميزات الجديدة - الجيل الثاني من الواجهات**

#### 🎨 **نظام الثيمات المتقدم**
- **4 ثيمات كاملة**: افتراضي، داكن، أزرق، أخضر
- **تبديل فوري للثيمات** أثناء التشغيل
- **ألوان متناسقة ومتكاملة** لكل ثيم
- **إدارة تلقائية للخطوط والأحجام**

#### 🧩 **مكونات واجهة محسنة**
- **بطاقات حديثة وتفاعلية** للمعلومات والإحصائيات
- **حقول إدخال ذكية** مع نصوص توضيحية
- **جداول تفاعلية** مع بحث وتصفية مدمجة
- **أزرار محسنة** مع أيقونات وتأثيرات
- **نوافذ تأكيد وتحميل متطورة**

#### 🖥️ **واجهات مطورة بالكامل**
- **النافذة الرئيسية**: شريط أدوات علوي + بطاقات إحصائية
- **إدارة الطلاب**: بحث متقدم + جدول تفاعلي + عمليات شاملة
- **تسجيل الغياب**: واجهة محسنة مع تصميم حديث
- **التقارير**: عرض متطور للبيانات والإحصائيات
- **رؤى الذكاء الصناعي**: تحليلات تفاعلية ومرئية

## ✨ **الميزات الأساسية - دعم RTL كامل**

### 🔄 **الاتجاه من اليمين إلى اليسار (RTL)**
- **محاذاة جميع النصوص إلى اليمين** بما يناسب القراءة العربية
- **ترتيب العناصر من اليمين إلى اليسار** في جميع النوافذ
- **موضع شريط التمرير على اليسار** كما هو متوقع في التطبيقات العربية
- **محاذاة أعمدة الجداول إلى اليمين** لسهولة القراءة

### 🎨 **تحسينات الخطوط والعرض**
- **خطوط محسنة للعربية**: Tahoma, Arial Unicode MS, Arial
- **اختيار تلقائي لأفضل خط متوفر** للعربية
- **تحسين عرض النصوص العربية** في جميع العناصر
- **دعم النصوص المختلطة** (عربي وإنجليزي)

### 🖥️ **واجهات محسنة**
- **النافذة الرئيسية**: الشريط الجانبي على اليمين، المحتوى على اليسار
- **إدارة الطلاب**: قائمة الطلاب على اليمين، التفاصيل على اليسار
- **تسجيل الغياب**: قائمة الطلاب على اليمين، الغيابات على اليسار
- **التقارير**: جميع العناصر مرتبة بما يناسب RTL
- **رؤى الذكاء الصناعي**: تحليلات مرتبة من اليمين إلى اليسار

### 🧪 **اختبار الميزات المتقدمة**

#### **اختبار الواجهات المحسنة**
```bash
python test_enhanced_ui.py
```
يعرض جميع المكونات الجديدة مع إمكانية تبديل الثيمات في الوقت الفعلي.

#### **اختبار دعم RTL**
```bash
python test_rtl.py
```
يفتح نافذة اختبار تعرض جميع العناصر مع دعم RTL لتأكيد عمل الميزات بشكل صحيح.

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd PP1
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

### 4. اختبار الواجهات المحسنة (اختياري)
```bash
python test_enhanced_ui.py
```
يعرض جميع المكونات الجديدة والثيمات المتاحة.

### 5. اختبار دعم RTL (اختياري)
```bash
python test_rtl.py
```
يفتح نافذة اختبار تعرض جميع العناصر مع دعم الاتجاه من اليمين إلى اليسار.

## دليل الاستخدام

### البدء السريع

1. **تشغيل البرنامج**: قم بتشغيل `main.py`
2. **إضافة الطلاب**: اذهب إلى "إدارة الطلاب" وأضف بيانات الطلاب
3. **تسجيل الغياب**: استخدم "تسجيل الغياب" لتسجيل الغيابات اليومية
4. **عرض التقارير**: اذهب إلى "التقارير" لعرض الإحصائيات

### الواجهات الرئيسية

#### 1. النافذة الرئيسية
- لوحة معلومات تعرض الإحصائيات السريعة
- روابط سريعة للوظائف الأساسية
- معلومات السنة والفصل الدراسي الحالي

#### 2. إدارة الطلاب
- إضافة طلاب جدد مع جميع البيانات المطلوبة
- تعديل بيانات الطلاب الموجودين
- البحث والتصفية حسب الاسم أو الرقم أو الصف
- حذف الطلاب (إلغاء تفعيل)

#### 3. تسجيل الغياب
- اختيار التاريخ ونوع الغياب
- تحديد الطلاب المتغيبين
- إضافة الأسباب والملاحظات
- عرض الغيابات المسجلة لليوم

#### 4. التقارير
- اختيار نوع التقرير والفترة الزمنية
- تصفية حسب الصف ونوع الغياب
- عرض الإحصائيات السريعة
- تصدير وطباعة التقارير

#### 5. رؤى الذكاء الصناعي
- تحليل أنماط الغياب
- قائمة الطلاب المعرضين للخطر
- توصيات لتحسين الحضور
- تدريب نموذج التنبؤ

## هيكل المشروع

```
PP1/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المتطلبات
├── README.md              # دليل المستخدم
├── data/                  # مجلد البيانات
│   ├── attendance.db      # قاعدة البيانات
│   ├── reports/           # التقارير المحفوظة
│   └── exports/           # الملفات المصدرة
└── src/                   # الكود المصدري
    ├── database/          # إدارة قاعدة البيانات
    │   └── database_manager.py
    ├── gui/               # واجهات المستخدم
    │   ├── main_window.py
    │   ├── students_management.py
    │   ├── attendance_recording.py
    │   ├── reports_window.py
    │   └── ai_insights.py
    ├── utils/             # الأدوات المساعدة
    │   ├── config.py
    │   └── export_utils.py
    └── ai/                # الذكاء الصناعي
        └── analytics.py
```

## الإعدادات والتخصيص

### إعدادات قاعدة البيانات
يمكن تعديل إعدادات قاعدة البيانات في `src/utils/config.py`:
- مسار قاعدة البيانات
- مهلة الاتصال
- إعدادات النسخ الاحتياطي

### إعدادات الواجهة
- ألوان النظام
- أحجام الخطوط
- أبعاد النوافذ
- اللغة والترجمة

### إعدادات الذكاء الصناعي
- عدد الأيام للتنبؤ
- الحد الأدنى من البيانات للتحليل
- معايير تحديد الطلاب المعرضين للخطر

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في تشغيل البرنامج
```
ModuleNotFoundError: No module named 'pandas'
```
**الحل**: تأكد من تثبيت جميع المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. خطأ في قاعدة البيانات
```
sqlite3.OperationalError: database is locked
```
**الحل**: تأكد من إغلاق جميع نسخ البرنامج وإعادة التشغيل

#### 3. مشاكل في التصدير
```
ImportError: reportlab not available
```
**الحل**: تثبيت مكتبة reportlab
```bash
pip install reportlab
```

### سجلات الأخطاء
يتم حفظ سجلات الأخطاء في مجلد `data/logs/` لمساعدة في استكشاف المشاكل.

## المساهمة في التطوير

نرحب بالمساهمات لتحسين البرنامج:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات والاختبارات
4. إرسال Pull Request

### معايير الكود
- استخدام التعليقات باللغة العربية
- اتباع معايير PEP 8
- كتابة اختبارات للميزات الجديدة
- توثيق الدوال والفئات

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراسلة المطور
- مراجعة الوثائق والأمثلة

## الإصدارات المستقبلية

### الميزات المخططة
- دعم قواعد بيانات خارجية (MySQL, PostgreSQL)
- واجهة ويب للوصول عن بُعد
- تطبيق جوال مصاحب
- تكامل مع أنظمة إدارة المدارس
- إشعارات تلقائية لأولياء الأمور
- تحليلات متقدمة بالذكاء الصناعي

---

**تم تطوير هذا البرنامج بواسطة مساعد الذكاء الصناعي**
**الإصدار: 1.0.0**
**تاريخ الإصدار: 2025-08-01**
