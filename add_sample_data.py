#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للنظام
لحل مشكلة الإحصائيات الفارغة
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

def add_sample_data():
    """إضافة بيانات تجريبية للنظام"""

    # الاتصال بقاعدة البيانات
    db_path = os.path.join('data', 'attendance.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        print("🔄 بدء إضافة البيانات التجريبية...")

        # التحقق من هيكل الجداول أولاً
        cursor.execute("PRAGMA table_info(students)")
        students_columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 أعمدة جدول الطلاب: {students_columns}")

        cursor.execute("PRAGMA table_info(attendance)")
        attendance_columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 أعمدة جدول الحضور: {attendance_columns}")

        # حذف البيانات الموجودة (اختياري)
        cursor.execute("DELETE FROM students WHERE student_id LIKE '2024%'")
        cursor.execute("DELETE FROM attendance WHERE student_id IN (SELECT id FROM students WHERE student_id LIKE '2024%')")

        # إضافة طلاب تجريبيين
        students_data = [
            ('2024001', 'أحمد محمد العلي', 2, 'أ', '0501234567', '0501234568'),
            ('2024002', 'خالد عبدالله السالم', 1, 'ب', '0507654321', '0507654322'),
            ('2024003', 'محمد سعد الأحمد', 3, 'أ', '0551234567', '0551234568'),
            ('2024004', 'عبدالرحمن أحمد الزهراني', 2, 'ج', '0559876543', '0559876544'),
            ('2024005', 'يوسف علي المطيري', 1, 'أ', '0503456789', '0503456790'),
            ('2024006', 'فهد سعود الغامدي', 3, 'ب', '0556789012', '0556789013'),
            ('2024007', 'عبدالعزيز محمد القحطاني', 2, 'أ', '0502345678', '0502345679'),
            ('2024008', 'سلطان عبدالله الشهري', 1, 'ج', '0558901234', '0558901235'),
            ('2024009', 'نايف أحمد العتيبي', 3, 'أ', '0504567890', '0504567891'),
            ('2024010', 'بندر سعد الدوسري', 2, 'ب', '0557890123', '0557890124'),
        ]

        for student in students_data:
            cursor.execute("""
                INSERT OR REPLACE INTO students
                (student_id, name, grade, class_section, phone, parent_phone, address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (*student, 'ضباء، منطقة تبوك'))

        print(f"✅ تم إضافة {len(students_data)} طالب")
        
        # الحصول على معرف الفصل الدراسي الحالي
        cursor.execute("SELECT id FROM semesters WHERE is_current = 1 LIMIT 1")
        semester_result = cursor.fetchone()
        if not semester_result:
            print("❌ لا يوجد فصل دراسي حالي")
            return
        semester_id = semester_result[0]

        # إضافة سجلات غياب
        attendance_records = []

        # تاريخ البداية (30 يوم مضت)
        start_date = datetime.now() - timedelta(days=30)

        for i, student in enumerate(students_data):
            student_id_text = student[0]

            # الحصول على معرف الطالب الرقمي
            cursor.execute("SELECT id FROM students WHERE student_id = ?", (student_id_text,))
            student_result = cursor.fetchone()
            if not student_result:
                continue
            student_db_id = student_result[0]

            absence_count = 0

            # إنشاء سجلات غياب لآخر 30 يوم
            for day in range(30):
                current_date = start_date + timedelta(days=day)

                # تجاهل عطلة نهاية الأسبوع
                if current_date.weekday() >= 5:  # السبت والأحد
                    continue

                # احتمالية الغياب (مختلفة لكل طالب)
                absence_probability = [0.05, 0.15, 0.25, 0.08, 0.12, 0.06, 0.04, 0.03, 0.02, 0.01][i]

                if random.random() < absence_probability:
                    # غياب
                    absence_type = 'unexcused'
                    absence_count += 1

                    attendance_records.append((
                        student_db_id,
                        semester_id,
                        current_date.strftime('%Y-%m-%d'),
                        absence_type,
                        'غياب بدون عذر',
                        'تسجيل تلقائي',
                        'system'
                    ))

        # إدراج سجلات الغياب
        if attendance_records:
            cursor.executemany("""
                INSERT OR IGNORE INTO attendance
                (student_id, semester_id, absence_date, absence_type, reason, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, attendance_records)

            print(f"✅ تم إضافة {len(attendance_records)} سجل غياب")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض الإحصائيات
        cursor.execute("SELECT COUNT(*) FROM students WHERE student_id LIKE '2024%'")
        total_students = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM attendance")
        total_absences = cursor.fetchone()[0]

        print("\n📊 إحصائيات النظام:")
        print(f"👥 إجمالي الطلاب المضافين: {total_students}")
        print(f"❌ إجمالي سجلات الغياب: {total_absences}")

        print("\n✅ تم إضافة البيانات التجريبية بنجاح!")
        print("🔄 يرجى إعادة تشغيل البرنامج لرؤية التحديثات")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {str(e)}")
        conn.rollback()
    
    finally:
        conn.close()

def verify_data():
    """التحقق من البيانات المضافة"""
    db_path = os.path.join('data', 'attendance.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        print("\n🔍 التحقق من البيانات...")

        # عدد الطلاب
        cursor.execute("SELECT COUNT(*) FROM students")
        students_count = cursor.fetchone()[0]
        print(f"👥 عدد الطلاب: {students_count}")

        # عدد سجلات الغياب
        cursor.execute("SELECT COUNT(*) FROM attendance")
        attendance_count = cursor.fetchone()[0]
        print(f"📝 سجلات الغياب: {attendance_count}")

        # عدد الغيابات حسب النوع
        cursor.execute("SELECT absence_type, COUNT(*) FROM attendance GROUP BY absence_type")
        absences_by_type = cursor.fetchall()
        print("❌ الغيابات حسب النوع:")
        for absence_type, count in absences_by_type:
            print(f"   {absence_type}: {count}")

        # عرض بعض الطلاب
        cursor.execute("SELECT student_id, name, grade FROM students LIMIT 5")
        sample_students = cursor.fetchall()
        print("\n👥 عينة من الطلاب:")
        for student in sample_students:
            print(f"   {student[0]} - {student[1]} - الصف {student[2]}")

    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")

    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 إضافة البيانات التجريبية لنظام إدارة الغياب")
    print("=" * 50)
    
    add_sample_data()
    verify_data()
    
    print("\n" + "=" * 50)
    print("✅ انتهت العملية بنجاح!")
    print("💡 نصيحة: أعد تشغيل البرنامج الرئيسي لرؤية البيانات الجديدة")
