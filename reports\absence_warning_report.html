<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير إنذارات الغياب - مدرسة ضباء المتوسطة للبنين</title>
    <style>
        body {
            font-family: 'Tahoma', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            direction: rtl;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.3"/><circle cx="80" cy="80" r="2" fill="white" opacity="0.3"/><circle cx="40" cy="60" r="1" fill="white" opacity="0.2"/></svg>');
            border-radius: 10px 10px 0 0;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            position: relative;
            z-index: 1;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .form-section {
            padding: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-title {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 15px -20px;
            font-weight: bold;
            font-size: 16px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-group label {
            font-weight: bold;
            color: #333;
        }
        
        .form-group input[type="text"], 
        .form-group select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
            transition: border-color 0.3s;
        }
        
        .form-group input[type="text"]:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .table-section {
            padding: 20px;
        }
        
        .absence-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .absence-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #5a6fd8;
        }
        
        .absence-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #ddd;
            transition: background-color 0.3s;
        }
        
        .absence-table tbody tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        
        .absence-table tbody tr:hover {
            background-color: #e3f2fd;
        }
        
        .warning-level {
            padding: 4px 8px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .warning-first { background: #ffa726; }
        .warning-second { background: #ff7043; }
        .warning-final { background: #e53935; }
        
        .signature-section {
            padding: 20px;
            border-top: 2px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .signature-box {
            text-align: center;
            padding: 15px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            min-width: 200px;
        }
        
        .print-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.3s;
            margin: 20px auto;
            display: block;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
        }
        
        @media print {
            body { background: white; }
            .print-btn { display: none; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير إنذارات الغياب</h1>
            <div class="subtitle">مدرسة ضباء المتوسطة للبنين - منطقة تبوك</div>
        </div>
        
        <div class="form-section">
            <div class="form-title">بيانات التقرير</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الفصل الدراسي:</label>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="first" name="semester" value="الأول" checked>
                            <label for="first">الأول</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="second" name="semester" value="الثاني">
                            <label for="second">الثاني</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="third" name="semester" value="الثالث">
                            <label for="third">الثالث</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الصف:</label>
                    <select>
                        <option>الأول المتوسط</option>
                        <option>الثاني المتوسط</option>
                        <option>الثالث المتوسط</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>الشعبة:</label>
                    <select>
                        <option>أ</option>
                        <option>ب</option>
                        <option>ج</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>تاريخ التقرير:</label>
                    <input type="text" value="1445/03/15" readonly>
                </div>
            </div>
        </div>
        
        <div class="table-section">
            <table class="absence-table">
                <thead>
                    <tr>
                        <th>رقم الطالب</th>
                        <th>اسم الطالب</th>
                        <th>عدد أيام الغياب</th>
                        <th>نوع الإنذار</th>
                        <th>تاريخ الإنذار</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024001</td>
                        <td>أحمد محمد العلي</td>
                        <td>8</td>
                        <td><span class="warning-level warning-first">إنذار أول</span></td>
                        <td>1445/03/10</td>
                        <td>غياب متكرر</td>
                    </tr>
                    <tr>
                        <td>2024002</td>
                        <td>خالد عبدالله السالم</td>
                        <td>15</td>
                        <td><span class="warning-level warning-second">إنذار ثاني</span></td>
                        <td>1445/03/12</td>
                        <td>تم إبلاغ ولي الأمر</td>
                    </tr>
                    <tr>
                        <td>2024003</td>
                        <td>محمد سعد الأحمد</td>
                        <td>22</td>
                        <td><span class="warning-level warning-final">إنذار نهائي</span></td>
                        <td>1445/03/14</td>
                        <td>مطلوب حضور ولي الأمر</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="signature-section">
            <div class="signature-box">
                <div><strong>إعداد:</strong></div>
                <div style="margin: 20px 0;">_________________</div>
                <div>مسؤول الحضور والغياب</div>
            </div>
            
            <div class="signature-box">
                <div><strong>اعتماد:</strong></div>
                <div style="margin: 20px 0;">_________________</div>
                <div>الأستاذ / عبدالله محمد الأحمد</div>
                <div>مدير المدرسة</div>
            </div>
        </div>
        
        <button class="print-btn" onclick="window.print()">طباعة التقرير</button>
    </div>
    
    <script>
        // تحديث التاريخ تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const hijriDate = today.toLocaleDateString('ar-SA-u-ca-islamic');
            document.querySelector('input[type="text"]').value = hijriDate;
        });
    </script>
</body>
</html>
