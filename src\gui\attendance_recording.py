#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الغياب
تحتوي على واجهة تسجيل الغياب اليومي مع تصنيف أنواع الغياب
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.database.database_manager import DatabaseManager
from src.utils.rtl_support import rtl_support

try:
    from tkcalendar import DateEntry
except ImportError:
    DateEntry = None

class AttendanceRecordingWindow:
    """فئة نافذة تسجيل الغياب"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة تسجيل الغياب"""
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.selected_students = []
        self.setup_window()
        self.create_interface()
        # تحميل البيانات بعد إنشاء الواجهة
        self.window.after(100, self.load_initial_data)
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("تسجيل الغياب")
        self.window.geometry("1200x800")
        self.window.minsize(1000, 700)
        
        # توسيط النافذة
        self.center_window()
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_initial_data(self):
        """تحميل البيانات الأولية بعد إنشاء الواجهة"""
        try:
            self.load_students()
            self.load_today_absences()
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات الأولية: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة تسجيل الغياب"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان
        arabic_font = rtl_support.get_arabic_font()
        title_label = rtl_support.create_rtl_label(main_frame, text="تسجيل الغياب", font=(arabic_font, 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # منطقة إعدادات التسجيل
        self.create_recording_settings(main_frame)

        # منطقة قائمة الطلاب (على اليمين في RTL)
        self.create_students_area(main_frame)

        # منطقة الغيابات المسجلة اليوم (على اليسار في RTL)
        self.create_today_absences_area(main_frame)

        # أزرار العمليات
        self.create_action_buttons(main_frame)

        # تطبيق إعدادات RTL
        rtl_support.apply_rtl_to_window(main_frame)
    
    def create_recording_settings(self, parent):
        """إنشاء منطقة إعدادات التسجيل"""
        settings_frame = ttk.LabelFrame(parent, text="إعدادات التسجيل", padding="10")
        settings_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure((1, 3, 5), weight=1)
        
        # تاريخ الغياب
        ttk.Label(settings_frame, text="تاريخ الغياب:").grid(row=0, column=0, padx=(0, 5), sticky=tk.W)
        
        if DateEntry:
            self.date_var = tk.StringVar()
            self.date_entry = DateEntry(settings_frame, textvariable=self.date_var, 
                                      date_pattern='yyyy-mm-dd', width=12)
            self.date_entry.set_date(date.today())
            self.date_entry.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        else:
            # استخدام Entry عادي إذا لم تكن tkcalendar متوفرة
            self.date_var = tk.StringVar()
            self.date_var.set(datetime.now().strftime("%Y-%m-%d"))
            date_entry = ttk.Entry(settings_frame, textvariable=self.date_var, width=15)
            date_entry.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        # نوع الغياب
        ttk.Label(settings_frame, text="نوع الغياب:").grid(row=0, column=2, padx=(10, 5), sticky=tk.W)
        self.absence_type_var = tk.StringVar()
        absence_combo = ttk.Combobox(settings_frame, textvariable=self.absence_type_var, 
                                   width=15, state="readonly")
        absence_combo['values'] = list(Config.ABSENCE_TYPES.values())
        absence_combo.set(Config.ABSENCE_TYPES['unexcused'])
        absence_combo.grid(row=0, column=3, padx=5, sticky=(tk.W, tk.E))
        
        # تصفية بالصف
        ttk.Label(settings_frame, text="تصفية بالصف:").grid(row=0, column=4, padx=(10, 5), sticky=tk.W)
        self.grade_filter_var = tk.StringVar()
        self.grade_filter_var.trace('w', self.on_grade_filter_change)
        grade_combo = ttk.Combobox(settings_frame, textvariable=self.grade_filter_var, 
                                 width=15, state="readonly")
        grade_combo['values'] = ['الكل'] + list(Config.GRADES.values())
        grade_combo.set('الكل')
        grade_combo.grid(row=0, column=5, padx=5, sticky=(tk.W, tk.E))
        
        # السبب والملاحظات
        ttk.Label(settings_frame, text="السبب:").grid(row=1, column=0, padx=(0, 5), pady=(10, 0), sticky=tk.W)
        self.reason_var = tk.StringVar()
        reason_entry = ttk.Entry(settings_frame, textvariable=self.reason_var, width=30)
        reason_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=(10, 0), sticky=(tk.W, tk.E))
        
        ttk.Label(settings_frame, text="ملاحظات:").grid(row=1, column=3, padx=(10, 5), pady=(10, 0), sticky=tk.W)
        self.notes_var = tk.StringVar()
        notes_entry = ttk.Entry(settings_frame, textvariable=self.notes_var, width=30)
        notes_entry.grid(row=1, column=4, columnspan=2, padx=5, pady=(10, 0), sticky=(tk.W, tk.E))
    
    def create_students_area(self, parent):
        """إنشاء منطقة قائمة الطلاب"""
        students_frame = ttk.LabelFrame(parent, text="قائمة الطلاب", padding="10")
        students_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        students_frame.columnconfigure(0, weight=1)
        students_frame.rowconfigure(1, weight=1)
        
        # منطقة البحث
        search_frame = ttk.Frame(students_frame)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, padx=(0, 5), sticky=tk.W)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=25)
        search_entry.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        # إنشاء جدول الطلاب
        columns = ('اختيار', 'رقم الطالب', 'الاسم', 'الصف', 'الشعبة')
        self.students_tree = ttk.Treeview(students_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        column_widths = {'اختيار': 60, 'رقم الطالب': 100, 'الاسم': 150, 'الصف': 100, 'الشعبة': 80}
        for col in columns:
            self.students_tree.heading(col, text=col)
            self.students_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير
        scrollbar1 = ttk.Scrollbar(students_frame, orient=tk.VERTICAL, command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar1.set)
        
        # ربط أحداث التحديد
        self.students_tree.bind('<Button-1>', self.on_student_click)
        self.students_tree.bind('<space>', self.on_student_space)
        
        # ترتيب العناصر
        self.students_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar1.grid(row=1, column=1, sticky=(tk.N, tk.S))
    
    def create_today_absences_area(self, parent):
        """إنشاء منطقة الغيابات المسجلة اليوم"""
        absences_frame = ttk.LabelFrame(parent, text="الغيابات المسجلة اليوم", padding="10")
        absences_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        absences_frame.columnconfigure(0, weight=1)
        absences_frame.rowconfigure(0, weight=1)
        
        # إنشاء جدول الغيابات
        columns = ('الطالب', 'النوع', 'السبب', 'الوقت')
        self.absences_tree = ttk.Treeview(absences_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        column_widths = {'الطالب': 150, 'النوع': 100, 'السبب': 120, 'الوقت': 80}
        for col in columns:
            self.absences_tree.heading(col, text=col)
            self.absences_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير
        scrollbar2 = ttk.Scrollbar(absences_frame, orient=tk.VERTICAL, command=self.absences_tree.yview)
        self.absences_tree.configure(yscrollcommand=scrollbar2.set)
        
        # ربط حدث التحديد للحذف
        self.absences_tree.bind('<Double-1>', self.on_absence_double_click)
        
        # ترتيب العناصر
        self.absences_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar2.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))
        
        # أزرار العمليات
        ttk.Button(buttons_frame, text="تسجيل الغياب", command=self.record_absence, 
                  width=15).grid(row=0, column=0, padx=5)
        ttk.Button(buttons_frame, text="تحديد الكل", command=self.select_all_students, 
                  width=15).grid(row=0, column=1, padx=5)
        ttk.Button(buttons_frame, text="إلغاء التحديد", command=self.deselect_all_students, 
                  width=15).grid(row=0, column=2, padx=5)
        ttk.Button(buttons_frame, text="تحديث القوائم", command=self.refresh_data, 
                  width=15).grid(row=0, column=3, padx=5)
        ttk.Button(buttons_frame, text="حذف غياب محدد", command=self.delete_selected_absence,
                  width=15).grid(row=0, column=4, padx=5)

    # ==================== دوال العمليات ====================

    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            # التحقق من وجود students_tree
            if not hasattr(self, 'students_tree'):
                print("❌ students_tree غير موجود بعد")
                return

            # مسح البيانات الحالية
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # تحديد الصف للتصفية
            grade = self.get_selected_grade()

            # الحصول على الطلاب من قاعدة البيانات
            students = self.db_manager.get_students(grade=grade)

            # إضافة الطلاب إلى الجدول
            for student in students:
                grade_name = Config.GRADES.get(student['grade'], str(student['grade']))
                self.students_tree.insert('', 'end', values=(
                    '☐',  # رمز عدم التحديد
                    student['student_id'],
                    student['name'],
                    grade_name,
                    student['class_section'] or ''
                ), tags=(student['id'], 'unselected'))

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل قائمة الطلاب: {str(e)}")

    def load_today_absences(self):
        """تحميل غيابات اليوم"""
        try:
            # التحقق من وجود absences_tree
            if not hasattr(self, 'absences_tree'):
                print("❌ absences_tree غير موجود بعد")
                return

            # مسح البيانات الحالية
            for item in self.absences_tree.get_children():
                self.absences_tree.delete(item)

            # الحصول على تاريخ اليوم المحدد
            absence_date = self.date_var.get()

            # الحصول على الغيابات من قاعدة البيانات
            absences = self.db_manager.get_daily_absences(absence_date)

            # إضافة الغيابات إلى الجدول
            for absence in absences:
                absence_type_name = Config.ABSENCE_TYPES.get(absence['absence_type'], absence['absence_type'])
                created_time = absence.get('created_at', '')
                if created_time:
                    try:
                        # استخراج الوقت فقط من التاريخ والوقت
                        time_part = created_time.split(' ')[1][:5] if ' ' in created_time else created_time[:5]
                    except:
                        time_part = ''
                else:
                    time_part = ''

                self.absences_tree.insert('', 'end', values=(
                    absence['student_name'],
                    absence_type_name,
                    absence.get('reason', ''),
                    time_part
                ), tags=(absence['id'],))

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل غيابات اليوم: {str(e)}")

    def get_selected_grade(self):
        """الحصول على الصف المحدد للتصفية"""
        grade_filter = self.grade_filter_var.get()
        if grade_filter == 'الكل':
            return None

        for grade_num, grade_name in Config.GRADES.items():
            if grade_name == grade_filter:
                return grade_num
        return None

    def get_absence_type_key(self, type_name):
        """الحصول على مفتاح نوع الغياب"""
        for key, name in Config.ABSENCE_TYPES.items():
            if name == type_name:
                return key
        return 'unexcused'

    def on_grade_filter_change(self, *args):
        """التعامل مع تغيير تصفية الصف"""
        self.load_students()

    def on_search_change(self, *args):
        """التعامل مع تغيير نص البحث"""
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.search_students)

    def search_students(self):
        """البحث عن الطلاب"""
        try:
            search_term = self.search_var.get().strip()
            grade = self.get_selected_grade()

            # الحصول على النتائج
            students = self.db_manager.get_students(grade=grade, search_term=search_term)

            # مسح البيانات الحالية
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # إضافة النتائج
            for student in students:
                grade_name = Config.GRADES.get(student['grade'], str(student['grade']))
                self.students_tree.insert('', 'end', values=(
                    '☐',
                    student['student_id'],
                    student['name'],
                    grade_name,
                    student['class_section'] or ''
                ), tags=(student['id'], 'unselected'))

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")

    def on_student_click(self, event):
        """التعامل مع النقر على طالب"""
        item = self.students_tree.identify('item', event.x, event.y)
        if item:
            self.toggle_student_selection(item)

    def on_student_space(self, event):
        """التعامل مع الضغط على مسافة لتحديد طالب"""
        selection = self.students_tree.selection()
        if selection:
            self.toggle_student_selection(selection[0])

    def toggle_student_selection(self, item):
        """تبديل تحديد طالب"""
        current_tags = self.students_tree.item(item, 'tags')
        student_id = current_tags[0]

        if 'selected' in current_tags:
            # إلغاء التحديد
            values = list(self.students_tree.item(item, 'values'))
            values[0] = '☐'
            self.students_tree.item(item, values=values, tags=(student_id, 'unselected'))
            if student_id in self.selected_students:
                self.selected_students.remove(student_id)
        else:
            # تحديد الطالب
            values = list(self.students_tree.item(item, 'values'))
            values[0] = '☑'
            self.students_tree.item(item, values=values, tags=(student_id, 'selected'))
            if student_id not in self.selected_students:
                self.selected_students.append(student_id)

    def select_all_students(self):
        """تحديد جميع الطلاب"""
        self.selected_students.clear()
        for item in self.students_tree.get_children():
            current_tags = self.students_tree.item(item, 'tags')
            student_id = current_tags[0]

            values = list(self.students_tree.item(item, 'values'))
            values[0] = '☑'
            self.students_tree.item(item, values=values, tags=(student_id, 'selected'))
            self.selected_students.append(student_id)

    def deselect_all_students(self):
        """إلغاء تحديد جميع الطلاب"""
        self.selected_students.clear()
        for item in self.students_tree.get_children():
            current_tags = self.students_tree.item(item, 'tags')
            student_id = current_tags[0]

            values = list(self.students_tree.item(item, 'values'))
            values[0] = '☐'
            self.students_tree.item(item, values=values, tags=(student_id, 'unselected'))

    def record_absence(self):
        """تسجيل الغياب للطلاب المحددين"""
        if not self.selected_students:
            messagebox.showwarning("تحذير", "يرجى تحديد طالب واحد على الأقل")
            return

        try:
            absence_date = self.date_var.get()
            absence_type = self.get_absence_type_key(self.absence_type_var.get())
            reason = self.reason_var.get().strip()
            notes = self.notes_var.get().strip()

            # التحقق من صحة التاريخ
            try:
                datetime.strptime(absence_date, "%Y-%m-%d")
            except ValueError:
                messagebox.showerror("خطأ", "تاريخ غير صحيح. يرجى استخدام تنسيق YYYY-MM-DD")
                return

            success_count = 0
            error_count = 0

            for student_id in self.selected_students:
                try:
                    absence_data = {
                        'student_id': student_id,
                        'absence_date': absence_date,
                        'absence_type': absence_type,
                        'reason': reason,
                        'notes': notes,
                        'created_by': 'المستخدم'
                    }

                    self.db_manager.add_absence(absence_data)
                    success_count += 1

                except Exception as e:
                    error_count += 1
                    print(f"خطأ في تسجيل غياب الطالب {student_id}: {str(e)}")

            # عرض النتيجة
            if success_count > 0:
                messagebox.showinfo("نجح", f"تم تسجيل {success_count} غياب بنجاح")

                # تحديث القوائم
                self.load_today_absences()
                self.deselect_all_students()

                # مسح الحقول
                self.reason_var.set('')
                self.notes_var.set('')

            if error_count > 0:
                messagebox.showwarning("تحذير", f"فشل في تسجيل {error_count} غياب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الغياب: {str(e)}")

    def on_absence_double_click(self, event):
        """التعامل مع النقر المزدوج على غياب لحذفه"""
        selection = self.absences_tree.selection()
        if selection:
            self.delete_selected_absence()

    def delete_selected_absence(self):
        """حذف الغياب المحدد"""
        selection = self.absences_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد غياب من القائمة")
            return

        item = selection[0]
        absence_id = self.absences_tree.item(item, 'tags')[0]
        student_name = self.absences_tree.item(item, 'values')[0]

        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف غياب الطالب '{student_name}'؟"):
            return

        try:
            success = self.db_manager.delete_absence(absence_id)

            if success:
                messagebox.showinfo("نجح", "تم حذف الغياب بنجاح")
                self.load_today_absences()
            else:
                messagebox.showerror("خطأ", "فشل في حذف الغياب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف الغياب: {str(e)}")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_students()
        self.load_today_absences()
        self.deselect_all_students()

    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        self.window.destroy()

# دالة لفتح نافذة تسجيل الغياب
def open_attendance_recording(parent=None):
    """فتح نافذة تسجيل الغياب"""
    return AttendanceRecordingWindow(parent)
