<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة إنذارات الغياب - مدرسة ضباء المتوسطة للبنين</title>
    <style>
        body {
            font-family: '<PERSON>homa', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            direction: rtl;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: move 20s linear infinite;
        }
        
        @keyframes move {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .controls-section {
            padding: 25px;
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 3px solid #dee2e6;
        }
        
        .controls-title {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 20px;
            margin: -25px -25px 20px -25px;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
        }
        
        .filter-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #495057;
            min-width: 100px;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 10px 15px;
            border: 2px solid #ced4da;
            border-radius: 8px;
            font-family: inherit;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            font-family: inherit;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .warnings-section {
            padding: 25px;
        }
        
        .warnings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .warning-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: all 0.3s;
        }
        
        .warning-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .warning-card.first { border-left-color: #ffc107; }
        .warning-card.second { border-left-color: #fd7e14; }
        .warning-card.final { border-left-color: #dc3545; }
        
        .warning-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .warning-type {
            padding: 5px 12px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .warning-type.first { background: #ffc107; }
        .warning-type.second { background: #fd7e14; }
        .warning-type.final { background: #dc3545; }
        
        .student-info h3 {
            margin: 0 0 10px 0;
            color: #343a40;
            font-size: 18px;
        }
        
        .student-details {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .absence-count {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            margin: 15px 0;
        }
        
        .absence-count .number {
            font-size: 24px;
            font-weight: bold;
            color: #dc3545;
        }
        
        .absence-count .label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-sm {
            padding: 5px 12px;
            font-size: 12px;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            text-align: center;
        }
        
        .summary-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .summary-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-number.first { color: #ffc107; }
        .summary-number.second { color: #fd7e14; }
        .summary-number.final { color: #dc3545; }
        .summary-number.total { color: #007bff; }
        
        @media print {
            body { background: white; }
            .btn { display: none; }
            .container { box-shadow: none; }
        }
        
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .warnings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>صفحة إنذارات الغياب</h1>
            <div class="subtitle">مدرسة ضباء المتوسطة للبنين - منطقة تبوك</div>
        </div>
        
        <div class="controls-section">
            <div class="controls-title">أدوات التحكم والبحث</div>
            
            <div class="filter-row">
                <div class="filter-group">
                    <label>الصف:</label>
                    <select id="gradeFilter">
                        <option value="">جميع الصفوف</option>
                        <option>الأول المتوسط</option>
                        <option>الثاني المتوسط</option>
                        <option>الثالث المتوسط</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>نوع الإنذار:</label>
                    <select id="warningFilter">
                        <option value="">جميع الإنذارات</option>
                        <option value="first">إنذار أول</option>
                        <option value="second">إنذار ثاني</option>
                        <option value="final">إنذار نهائي</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>البحث:</label>
                    <input type="text" id="searchInput" placeholder="اسم الطالب أو رقمه">
                </div>
                
                <button class="btn btn-primary" onclick="filterWarnings()">بحث</button>
                <button class="btn btn-success" onclick="exportReport()">تصدير تقرير</button>
            </div>
        </div>
        
        <div class="warnings-section">
            <div class="summary-section">
                <h3 style="text-align: center; margin-bottom: 20px;">ملخص الإنذارات</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-number first">12</div>
                        <div>إنذار أول</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number second">8</div>
                        <div>إنذار ثاني</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number final">3</div>
                        <div>إنذار نهائي</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number total">23</div>
                        <div>إجمالي الإنذارات</div>
                    </div>
                </div>
            </div>
            
            <div class="warnings-grid" id="warningsContainer">
                <div class="warning-card first">
                    <div class="warning-header">
                        <span class="warning-type first">إنذار أول</span>
                        <span style="color: #6c757d; font-size: 12px;">1445/03/10</span>
                    </div>
                    <div class="student-info">
                        <h3>أحمد محمد العلي</h3>
                        <div class="student-details">
                            رقم الطالب: 2024001<br>
                            الصف: الثاني المتوسط - شعبة أ
                        </div>
                    </div>
                    <div class="absence-count">
                        <div class="number">8</div>
                        <div class="label">أيام غياب</div>
                    </div>
                    <div class="actions">
                        <button class="btn btn-warning btn-sm">إرسال إشعار</button>
                        <button class="btn btn-primary btn-sm">تفاصيل</button>
                    </div>
                </div>
                
                <div class="warning-card second">
                    <div class="warning-header">
                        <span class="warning-type second">إنذار ثاني</span>
                        <span style="color: #6c757d; font-size: 12px;">1445/03/12</span>
                    </div>
                    <div class="student-info">
                        <h3>خالد عبدالله السالم</h3>
                        <div class="student-details">
                            رقم الطالب: 2024002<br>
                            الصف: الأول المتوسط - شعبة ب
                        </div>
                    </div>
                    <div class="absence-count">
                        <div class="number">15</div>
                        <div class="label">أيام غياب</div>
                    </div>
                    <div class="actions">
                        <button class="btn btn-warning btn-sm">اتصال بولي الأمر</button>
                        <button class="btn btn-primary btn-sm">تفاصيل</button>
                    </div>
                </div>
                
                <div class="warning-card final">
                    <div class="warning-header">
                        <span class="warning-type final">إنذار نهائي</span>
                        <span style="color: #6c757d; font-size: 12px;">1445/03/14</span>
                    </div>
                    <div class="student-info">
                        <h3>محمد سعد الأحمد</h3>
                        <div class="student-details">
                            رقم الطالب: 2024003<br>
                            الصف: الثالث المتوسط - شعبة أ
                        </div>
                    </div>
                    <div class="absence-count">
                        <div class="number">22</div>
                        <div class="label">أيام غياب</div>
                    </div>
                    <div class="actions">
                        <button class="btn btn-warning btn-sm">استدعاء عاجل</button>
                        <button class="btn btn-primary btn-sm">تفاصيل</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function filterWarnings() {
            const grade = document.getElementById('gradeFilter').value;
            const warning = document.getElementById('warningFilter').value;
            const search = document.getElementById('searchInput').value.toLowerCase();
            
            const cards = document.querySelectorAll('.warning-card');
            
            cards.forEach(card => {
                let show = true;
                
                if (warning && !card.classList.contains(warning)) {
                    show = false;
                }
                
                if (search) {
                    const studentName = card.querySelector('h3').textContent.toLowerCase();
                    const studentId = card.querySelector('.student-details').textContent.toLowerCase();
                    if (!studentName.includes(search) && !studentId.includes(search)) {
                        show = false;
                    }
                }
                
                card.style.display = show ? 'block' : 'none';
            });
        }
        
        function exportReport() {
            window.print();
        }
        
        // تحديث الوقت الحقيقي
        setInterval(() => {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            console.log('آخر تحديث:', timeString);
        }, 60000);
    </script>
</body>
</html>
