#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
نافذة شاملة لإدارة جميع إعدادات البرنامج
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import json
import os
import sys
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.utils.rtl_support import rtl_support
from src.utils.themes import theme_manager
from src.gui.components import ModernCard, ModernEntry, ModernCombobox, ModernButton, ConfirmDialog, LoadingDialog
from src.database.database_manager import DatabaseManager

class SettingsWindow:
    """نافذة الإعدادات الشاملة"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة الإعدادات"""
        self.parent = parent
        self.window = None
        self.settings = {}
        self.db_manager = DatabaseManager()
        self.create_window()
    
    def create_window(self):
        """إنشاء نافذة الإعدادات"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("⚙️ إعدادات البرنامج")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_content()
        
        # تحميل الإعدادات الحالية
        self.load_current_settings()
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # تطبيق RTL
        rtl_support.apply_rtl_to_window(self.window)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 900
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)
        
        # دفتر التبويبات
        self.create_notebook(main_frame)
        
        # أزرار العمليات
        self.create_action_buttons(main_frame)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_frame = ttk.Frame(parent, style='Sidebar.TFrame', padding="15")
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        toolbar_frame.columnconfigure(1, weight=1)
        
        # العنوان مع الأيقونة
        title_frame = ttk.Frame(toolbar_frame)
        title_frame.grid(row=0, column=0, sticky=tk.E)
        
        icon_label = rtl_support.create_rtl_label(title_frame, text="⚙️", font=("Arial", 24))
        icon_label.grid(row=0, column=0, padx=(0, 15))
        
        title_label = rtl_support.create_rtl_label(title_frame, text="إعدادات البرنامج", style='Title.TLabel')
        title_label.grid(row=0, column=1)
        
        # معلومات الإصدار
        info_frame = ttk.Frame(toolbar_frame)
        info_frame.grid(row=0, column=1, padx=(20, 0))
        
        version_label = rtl_support.create_rtl_label(info_frame, text=f"الإصدار: {Config.APP_VERSION}", style='Info.TLabel')
        version_label.grid(row=0, column=0)
        
        # أزرار سريعة
        quick_buttons_frame = ttk.Frame(toolbar_frame)
        quick_buttons_frame.grid(row=0, column=2, sticky=tk.W)
        
        ModernButton(quick_buttons_frame, text="🔄 إعادة تعيين", command=self.reset_to_defaults, 
                    style="Warning.TButton").grid(row=0, column=0, padx=2)
        ModernButton(quick_buttons_frame, text="📥 استيراد", command=self.import_settings, 
                    style="TButton").grid(row=0, column=1, padx=2)
        ModernButton(quick_buttons_frame, text="📤 تصدير", command=self.export_settings, 
                    style="TButton").grid(row=0, column=2, padx=2)
    
    def create_notebook(self, parent):
        """إنشاء دفتر التبويبات"""
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        
        # تبويب المظهر والثيمات
        self.create_appearance_tab()
        
        # تبويب قاعدة البيانات
        self.create_database_tab()
        
        # تبويب التقارير والتصدير
        self.create_reports_tab()
        
        # تبويب الذكاء الصناعي
        self.create_ai_tab()
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab()
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        general_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(general_frame, text="🏠 عام")
        general_frame.columnconfigure(0, weight=1)
        
        # بطاقة معلومات المدرسة
        school_card = ModernCard(general_frame, title="🏫 معلومات المدرسة")
        school_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        school_content = school_card.content_frame
        school_content.columnconfigure(0, weight=1)
        
        self.school_name = ModernEntry(school_content, "اسم المدرسة", placeholder="ادخل اسم المدرسة...")
        self.school_name.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.school_address = ModernEntry(school_content, "عنوان المدرسة", placeholder="ادخل عنوان المدرسة...")
        self.school_address.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.school_phone = ModernEntry(school_content, "هاتف المدرسة", placeholder="05xxxxxxxx")
        self.school_phone.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # بطاقة إعدادات النظام
        system_card = ModernCard(general_frame, title="🔧 إعدادات النظام")
        system_card.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        system_content = system_card.content_frame
        system_content.columnconfigure(0, weight=1)
        
        self.auto_backup = tk.BooleanVar()
        auto_backup_check = ttk.Checkbutton(system_content, text="النسخ الاحتياطي التلقائي", 
                                          variable=self.auto_backup)
        auto_backup_check.grid(row=0, column=0, sticky=tk.E, pady=5)
        
        self.startup_check = tk.BooleanVar()
        startup_check_box = ttk.Checkbutton(system_content, text="فحص التحديثات عند البدء", 
                                          variable=self.startup_check)
        startup_check_box.grid(row=1, column=0, sticky=tk.E, pady=5)
        
        self.notifications = tk.BooleanVar()
        notifications_check = ttk.Checkbutton(system_content, text="تفعيل الإشعارات", 
                                            variable=self.notifications)
        notifications_check.grid(row=2, column=0, sticky=tk.E, pady=5)
        
        # بطاقة إعدادات اللغة
        language_card = ModernCard(general_frame, title="🌐 إعدادات اللغة")
        language_card.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        language_content = language_card.content_frame
        language_content.columnconfigure(0, weight=1)
        
        self.language = ModernCombobox(language_content, "لغة الواجهة", 
                                     values=["العربية", "English"])
        self.language.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.date_format = ModernCombobox(language_content, "تنسيق التاريخ", 
                                        values=["YYYY-MM-DD", "DD/MM/YYYY", "MM/DD/YYYY"])
        self.date_format.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def create_appearance_tab(self):
        """إنشاء تبويب المظهر والثيمات"""
        appearance_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(appearance_frame, text="🎨 المظهر")
        appearance_frame.columnconfigure(0, weight=1)
        
        # بطاقة الثيمات
        theme_card = ModernCard(appearance_frame, title="🎨 الثيمات")
        theme_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        theme_content = theme_card.content_frame
        theme_content.columnconfigure((0, 1, 2, 3), weight=1)
        
        # أزرار الثيمات
        themes = [("افتراضي", "default"), ("داكن", "dark"), ("أزرق", "blue"), ("أخضر", "green")]
        self.theme_var = tk.StringVar(value=theme_manager.current_theme)
        
        for i, (name, theme_id) in enumerate(themes):
            theme_btn = ttk.Radiobutton(theme_content, text=name, value=theme_id, 
                                      variable=self.theme_var, command=self.preview_theme)
            theme_btn.grid(row=0, column=i, padx=10, pady=10, sticky=(tk.W, tk.E))
        
        # معاينة الثيم
        preview_frame = ttk.LabelFrame(theme_content, text="معاينة", padding="10")
        preview_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.preview_label = rtl_support.create_rtl_label(preview_frame, 
                                                        text="هذا نص تجريبي لمعاينة الثيم المختار", 
                                                        style='Header.TLabel')
        self.preview_label.grid(row=0, column=0, pady=10)
        
        # بطاقة الخطوط
        font_card = ModernCard(appearance_frame, title="🔤 الخطوط")
        font_card.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        font_content = font_card.content_frame
        font_content.columnconfigure(0, weight=1)
        
        self.font_family = ModernCombobox(font_content, "نوع الخط", 
                                        values=["Tahoma", "Arial Unicode MS", "Arial", "Segoe UI"])
        self.font_family.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.font_size = ModernCombobox(font_content, "حجم الخط", 
                                      values=["10", "11", "12", "13", "14", "15", "16"])
        self.font_size.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # بطاقة إعدادات RTL
        rtl_card = ModernCard(appearance_frame, title="🔄 إعدادات RTL")
        rtl_card.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        rtl_content = rtl_card.content_frame
        
        self.rtl_enabled = tk.BooleanVar(value=True)
        rtl_check = ttk.Checkbutton(rtl_content, text="تفعيل الاتجاه من اليمين إلى اليسار", 
                                  variable=self.rtl_enabled)
        rtl_check.grid(row=0, column=0, sticky=tk.E, pady=5)
        
        self.arabic_numbers = tk.BooleanVar(value=True)
        arabic_numbers_check = ttk.Checkbutton(rtl_content, text="استخدام الأرقام العربية", 
                                             variable=self.arabic_numbers)
        arabic_numbers_check.grid(row=1, column=0, sticky=tk.E, pady=5)
    
    def create_database_tab(self):
        """إنشاء تبويب قاعدة البيانات"""
        db_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(db_frame, text="🗄️ قاعدة البيانات")
        db_frame.columnconfigure(0, weight=1)
        
        # بطاقة معلومات قاعدة البيانات
        db_info_card = ModernCard(db_frame, title="📊 معلومات قاعدة البيانات")
        db_info_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        db_info_content = db_info_card.content_frame
        
        # معلومات قاعدة البيانات
        self.db_info_label = rtl_support.create_rtl_label(db_info_content, 
                                                        text="جاري تحميل معلومات قاعدة البيانات...", 
                                                        style='Info.TLabel')
        self.db_info_label.grid(row=0, column=0, sticky=tk.E, pady=5)
        
        # أزرار إدارة قاعدة البيانات
        db_buttons_frame = ttk.Frame(db_info_content)
        db_buttons_frame.grid(row=1, column=0, pady=(10, 0), sticky=tk.E)
        
        ModernButton(db_buttons_frame, text="🔧 صيانة", command=self.maintain_database, 
                    style="Warning.TButton").grid(row=0, column=0, padx=5)
        ModernButton(db_buttons_frame, text="📊 إحصائيات", command=self.show_db_stats, 
                    style="TButton").grid(row=0, column=1, padx=5)
        ModernButton(db_buttons_frame, text="🗑️ تنظيف", command=self.clean_database, 
                    style="Danger.TButton").grid(row=0, column=2, padx=5)
        
        # بطاقة إعدادات الأداء
        performance_card = ModernCard(db_frame, title="⚡ إعدادات الأداء")
        performance_card.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        performance_content = performance_card.content_frame
        performance_content.columnconfigure(0, weight=1)
        
        self.cache_size = ModernCombobox(performance_content, "حجم ذاكرة التخزين المؤقت", 
                                       values=["صغير", "متوسط", "كبير"])
        self.cache_size.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.auto_optimize = tk.BooleanVar()
        auto_optimize_check = ttk.Checkbutton(performance_content, text="تحسين تلقائي لقاعدة البيانات", 
                                            variable=self.auto_optimize)
        auto_optimize_check.grid(row=1, column=0, sticky=tk.E, pady=5)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # تحميل الإعدادات المحفوظة
            settings = self.load_saved_settings()

            # تحميل إعدادات المدرسة
            self.school_name.set(settings.get('school_name', getattr(Config, 'SCHOOL_NAME', '')))
            self.school_address.set(settings.get('school_address', getattr(Config, 'SCHOOL_ADDRESS', '')))
            self.school_phone.set(settings.get('school_phone', getattr(Config, 'SCHOOL_PHONE', '')))

            # تحميل إعدادات المدير
            self.principal_name.set(settings.get('principal_name', getattr(Config, 'PRINCIPAL_NAME', '')))
            self.principal_title.set(settings.get('principal_title', getattr(Config, 'PRINCIPAL_TITLE', '')))
            self.signature_text.set(settings.get('signature_text', getattr(Config, 'PRINCIPAL_SIGNATURE', '')))

            # تحميل إعدادات الختم
            self.stamp_text.set(settings.get('stamp_text', getattr(Config, 'STAMP_PLACEHOLDER', '')))
            self.show_signature.set(settings.get('show_signature', True))
            self.show_stamp.set(settings.get('show_stamp', True))

            # تحميل إعدادات النظام
            self.auto_backup.set(getattr(Config, 'AUTO_BACKUP', True))
            self.startup_check.set(getattr(Config, 'STARTUP_CHECK', True))
            self.notifications.set(getattr(Config, 'NOTIFICATIONS', True))

            # تحميل إعدادات المظهر
            self.theme_var.set(theme_manager.current_theme)
            self.font_family.set(getattr(Config, 'FONT_FAMILY', 'Tahoma'))
            self.font_size.set(str(getattr(Config, 'FONT_SIZE', 12)))

            # تحميل إعدادات التقارير
            self.default_report_format.set(settings.get('default_report_format', 'PDF'))
            self.include_charts.set(settings.get('include_charts', True))

            # تحميل معلومات قاعدة البيانات
            self.load_database_info()

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {str(e)}")

    def load_saved_settings(self):
        """تحميل الإعدادات المحفوظة من الملف"""
        settings_file = os.path.join(Config.DATA_DIR, "settings.json")
        default_settings = {}

        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    default_settings.update(saved_settings)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات المحفوظة: {e}")

        return default_settings
    
    def load_database_info(self):
        """تحميل معلومات قاعدة البيانات"""
        try:
            # الحصول على إحصائيات قاعدة البيانات
            students_count = len(self.db_manager.get_students())
            
            # حجم ملف قاعدة البيانات
            db_path = self.db_manager.db_path
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path) / 1024  # بالكيلوبايت
                db_size_text = f"{db_size:.1f} KB"
            else:
                db_size_text = "غير محدد"
            
            info_text = f"عدد الطلاب: {students_count}\nحجم قاعدة البيانات: {db_size_text}\nآخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            self.db_info_label.config(text=info_text)
            
        except Exception as e:
            self.db_info_label.config(text=f"خطأ في تحميل معلومات قاعدة البيانات: {str(e)}")
    
    def preview_theme(self):
        """معاينة الثيم المختار"""
        selected_theme = self.theme_var.get()
        theme_manager.set_theme(selected_theme)
        
        # تطبيق الثيم على النافذة الحالية
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # تحديث نص المعاينة
        theme_name = theme_manager.get_current_theme()['name']
        self.preview_label.config(text=f"معاينة الثيم: {theme_name}")
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(reports_frame, text="📊 التقارير")
        reports_frame.columnconfigure(0, weight=1)

        # معلومات المدرسة
        school_card = ModernCard(reports_frame, title="🏫 معلومات المدرسة")
        school_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        school_content = school_card.content_frame
        school_content.columnconfigure(0, weight=1)

        self.school_name = ModernEntry(school_content, "اسم المدرسة")
        self.school_name.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)

        self.school_address = ModernEntry(school_content, "عنوان المدرسة")
        self.school_address.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        self.school_phone = ModernEntry(school_content, "هاتف المدرسة")
        self.school_phone.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=5)

        # معلومات المدير
        principal_card = ModernCard(reports_frame, title="👨‍💼 معلومات المدير")
        principal_card.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        principal_content = principal_card.content_frame
        principal_content.columnconfigure(0, weight=1)

        self.principal_name = ModernEntry(principal_content, "اسم المدير")
        self.principal_name.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)

        self.principal_title = ModernEntry(principal_content, "منصب المدير")
        self.principal_title.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        self.signature_text = ModernEntry(principal_content, "نص التوقيع")
        self.signature_text.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=5)

        # إعدادات الختم والتوقيع
        stamp_card = ModernCard(reports_frame, title="🔖 إعدادات الختم والتوقيع")
        stamp_card.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        stamp_content = stamp_card.content_frame
        stamp_content.columnconfigure(0, weight=1)

        self.stamp_text = ModernEntry(stamp_content, "نص الختم")
        self.stamp_text.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)

        # خيارات العرض
        self.show_signature = tk.BooleanVar(value=True)
        signature_check = ttk.Checkbutton(stamp_content, text="عرض توقيع المدير في التقارير",
                                        variable=self.show_signature)
        signature_check.grid(row=1, column=0, sticky=tk.E, pady=5)

        self.show_stamp = tk.BooleanVar(value=True)
        stamp_check = ttk.Checkbutton(stamp_content, text="عرض الختم في التقارير",
                                    variable=self.show_stamp)
        stamp_check.grid(row=2, column=0, sticky=tk.E, pady=5)

        # إعدادات التقارير العامة
        general_card = ModernCard(reports_frame, title="📄 إعدادات التقارير العامة")
        general_card.grid(row=3, column=0, sticky=(tk.W, tk.E))

        general_content = general_card.content_frame
        general_content.columnconfigure(0, weight=1)

        self.default_report_format = ModernCombobox(general_content, "تنسيق التقرير الافتراضي",
                                                  values=["PDF", "Excel", "Word"])
        self.default_report_format.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)

        self.include_charts = tk.BooleanVar(value=True)
        charts_check = ttk.Checkbutton(general_content, text="تضمين الرسوم البيانية",
                                     variable=self.include_charts)
        charts_check.grid(row=1, column=0, sticky=tk.E, pady=5)
    
    def create_ai_tab(self):
        """إنشاء تبويب الذكاء الصناعي"""
        ai_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(ai_frame, text="🤖 الذكاء الصناعي")
        
        # محتوى مبسط للذكاء الصناعي
        ai_card = ModernCard(ai_frame, title="🧠 إعدادات الذكاء الصناعي")
        ai_card.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        ai_content = ai_card.content_frame
        
        self.ai_enabled = tk.BooleanVar(value=True)
        ai_check = ttk.Checkbutton(ai_content, text="تفعيل ميزات الذكاء الصناعي", 
                                 variable=self.ai_enabled)
        ai_check.grid(row=0, column=0, sticky=tk.E, pady=5)
        
        self.auto_predictions = tk.BooleanVar(value=True)
        predictions_check = ttk.Checkbutton(ai_content, text="التنبؤات التلقائية", 
                                          variable=self.auto_predictions)
        predictions_check.grid(row=1, column=0, sticky=tk.E, pady=5)
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(backup_frame, text="💾 النسخ الاحتياطي")
        
        # محتوى مبسط للنسخ الاحتياطي
        backup_card = ModernCard(backup_frame, title="🔄 إعدادات النسخ الاحتياطي")
        backup_card.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        backup_content = backup_card.content_frame
        backup_content.columnconfigure(0, weight=1)
        
        self.backup_frequency = ModernCombobox(backup_content, "تكرار النسخ الاحتياطي", 
                                             values=["يومي", "أسبوعي", "شهري"])
        self.backup_frequency.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = ttk.Frame(backup_content)
        backup_buttons_frame.grid(row=1, column=0, pady=(10, 0), sticky=tk.E)
        
        ModernButton(backup_buttons_frame, text="💾 نسخ احتياطي الآن", command=self.create_backup, 
                    style="Success.TButton").grid(row=0, column=0, padx=5)
        ModernButton(backup_buttons_frame, text="📂 استعادة", command=self.restore_backup, 
                    style="Warning.TButton").grid(row=0, column=1, padx=5)
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # أزرار العمليات
        ModernButton(buttons_frame, text="💾 حفظ الإعدادات", command=self.save_settings, 
                    style="Success.TButton").pack(side=tk.RIGHT, padx=5)
        ModernButton(buttons_frame, text="🔄 تطبيق", command=self.apply_settings, 
                    style="Primary.TButton").pack(side=tk.RIGHT, padx=5)
        ModernButton(buttons_frame, text="❌ إلغاء", command=self.on_closing, 
                    style="TButton").pack(side=tk.RIGHT, padx=5)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            loading = LoadingDialog(self.window, "جاري حفظ الإعدادات...")
            
            # حفظ الإعدادات في ملف
            settings = {
                # معلومات المدرسة
                'school_name': self.school_name.get(),
                'school_address': self.school_address.get(),
                'school_phone': self.school_phone.get(),

                # معلومات المدير
                'principal_name': self.principal_name.get(),
                'principal_title': self.principal_title.get(),
                'signature_text': self.signature_text.get(),

                # إعدادات الختم والتوقيع
                'stamp_text': self.stamp_text.get(),
                'show_signature': self.show_signature.get(),
                'show_stamp': self.show_stamp.get(),

                # إعدادات التقارير
                'default_report_format': self.default_report_format.get(),
                'include_charts': self.include_charts.get(),

                # إعدادات المظهر والنظام
                'theme': self.theme_var.get(),
                'font_family': self.font_family.get(),
                'font_size': self.font_size.get(),
                'auto_backup': self.auto_backup.get(),
                'notifications': self.notifications.get(),
                'rtl_enabled': self.rtl_enabled.get(),
                'last_updated': datetime.now().isoformat()
            }
            
            # حفظ في ملف JSON
            settings_file = os.path.join(os.path.dirname(Config.DATABASE_PATH), 'settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            loading.close()
            messagebox.showinfo("تم", "تم حفظ الإعدادات بنجاح!")
            
        except Exception as e:
            if 'loading' in locals():
                loading.close()
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            # تطبيق الثيم
            theme_manager.set_theme(self.theme_var.get())
            style = ttk.Style()
            theme_manager.apply_theme_to_style(style)
            
            messagebox.showinfo("تم", "تم تطبيق الإعدادات!")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تطبيق الإعدادات: {str(e)}")
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        dialog = ConfirmDialog(self.window, "تأكيد", "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")
        if dialog.show():
            # إعادة تعيين القيم
            self.theme_var.set("default")
            self.font_family.set("Tahoma")
            self.font_size.set("12")
            self.auto_backup.set(True)
            self.notifications.set(True)
            self.rtl_enabled.set(True)
            
            messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات الافتراضية!")
    
    def import_settings(self):
        """استيراد الإعدادات"""
        messagebox.showinfo("قيد التطوير", "ميزة استيراد الإعدادات قيد التطوير")
    
    def export_settings(self):
        """تصدير الإعدادات"""
        messagebox.showinfo("قيد التطوير", "ميزة تصدير الإعدادات قيد التطوير")
    
    def maintain_database(self):
        """صيانة قاعدة البيانات"""
        messagebox.showinfo("قيد التطوير", "ميزة صيانة قاعدة البيانات قيد التطوير")
    
    def show_db_stats(self):
        """عرض إحصائيات قاعدة البيانات"""
        messagebox.showinfo("قيد التطوير", "ميزة إحصائيات قاعدة البيانات قيد التطوير")
    
    def clean_database(self):
        """تنظيف قاعدة البيانات"""
        messagebox.showinfo("قيد التطوير", "ميزة تنظيف قاعدة البيانات قيد التطوير")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        messagebox.showinfo("قيد التطوير", "ميزة النسخ الاحتياطي قيد التطوير")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        messagebox.showinfo("قيد التطوير", "ميزة استعادة النسخ الاحتياطي قيد التطوير")
    
    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        self.window.destroy()

def open_settings_window(parent=None):
    """فتح نافذة الإعدادات"""
    settings_window = SettingsWindow(parent)
    return settings_window

if __name__ == "__main__":
    # اختبار نافذة الإعدادات
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    settings = SettingsWindow()
    root.mainloop()
