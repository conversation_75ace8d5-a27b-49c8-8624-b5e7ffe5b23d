#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة الإعدادات
اختبار شامل لنافذة الإعدادات والميزات المتقدمة
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_settings_window():
    """اختبار نافذة الإعدادات"""
    print("🔍 اختبار نافذة الإعدادات...")
    
    try:
        from src.gui.settings_window import SettingsWindow
        from src.utils.themes import theme_manager
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار نافذة الإعدادات")
        root.geometry("400x300")
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🧪 اختبار نافذة الإعدادات", 
                               font=("Tahoma", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # معلومات الاختبار
        info_text = """
        هذا اختبار لنافذة الإعدادات الجديدة.
        
        الميزات المتاحة:
        • إعدادات عامة للمدرسة والنظام
        • تخصيص المظهر والثيمات
        • إدارة قاعدة البيانات
        • إعدادات التقارير والتصدير
        • تكوين الذكاء الصناعي
        • إدارة النسخ الاحتياطي
        
        انقر على الزر أدناه لفتح نافذة الإعدادات.
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.CENTER)
        info_label.pack(pady=(0, 20))
        
        # زر فتح الإعدادات
        def open_settings():
            try:
                settings_window = SettingsWindow(root)
                print("✅ تم فتح نافذة الإعدادات بنجاح")
            except Exception as e:
                print(f"❌ خطأ في فتح نافذة الإعدادات: {str(e)}")
                import traceback
                traceback.print_exc()
        
        settings_btn = ttk.Button(main_frame, text="⚙️ فتح نافذة الإعدادات", 
                                 command=open_settings)
        settings_btn.pack(pady=10)
        
        # زر اختبار الثيمات
        def test_themes():
            themes = ["default", "dark", "blue", "green"]
            current_index = themes.index(theme_manager.current_theme)
            next_theme = themes[(current_index + 1) % len(themes)]
            
            theme_manager.set_theme(next_theme)
            theme_manager.apply_theme_to_style(style)
            
            theme_name = theme_manager.get_current_theme()['name']
            print(f"🎨 تم تغيير الثيم إلى: {theme_name}")
        
        theme_btn = ttk.Button(main_frame, text="🎨 تبديل الثيم", 
                              command=test_themes)
        theme_btn.pack(pady=5)
        
        # معلومات إضافية
        footer_label = ttk.Label(main_frame, 
                                text="تحقق من جميع التبويبات والميزات في نافذة الإعدادات", 
                                font=("Tahoma", 10), foreground="gray")
        footer_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
        print("✅ تم إنشاء نافذة اختبار الإعدادات بنجاح")
        print("📝 تحقق من:")
        print("   - فتح نافذة الإعدادات")
        print("   - التنقل بين التبويبات المختلفة")
        print("   - تغيير الثيمات ومعاينتها")
        print("   - حفظ وتطبيق الإعدادات")
        print("   - إعادة تعيين الإعدادات الافتراضية")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة الإعدادات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_features():
    """اختبار ميزات الإعدادات"""
    print("\n⚙️ اختبار ميزات الإعدادات...")
    
    try:
        from src.utils.themes import theme_manager
        from src.utils.config import Config
        
        # اختبار الثيمات
        print("🎨 اختبار الثيمات:")
        themes = theme_manager.get_available_themes()
        for theme_id, theme_name in themes:
            success = theme_manager.set_theme(theme_id)
            if success:
                print(f"  ✅ {theme_name} - يعمل")
            else:
                print(f"  ❌ {theme_name} - لا يعمل")
        
        # اختبار الإعدادات
        print("\n📋 اختبار الإعدادات:")
        settings_to_test = [
            ('APP_NAME', 'اسم البرنامج'),
            ('APP_VERSION', 'إصدار البرنامج'),
            ('DATABASE_PATH', 'مسار قاعدة البيانات'),
            ('GRADES', 'الصفوف الدراسية')
        ]
        
        for setting, description in settings_to_test:
            if hasattr(Config, setting):
                value = getattr(Config, setting)
                print(f"  ✅ {description}: {value}")
            else:
                print(f"  ❌ {description}: غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ميزات الإعدادات: {str(e)}")
        return False

def test_settings_integration():
    """اختبار تكامل الإعدادات مع النظام"""
    print("\n🔗 اختبار تكامل الإعدادات...")
    
    try:
        # اختبار استيراد المكونات
        from src.gui.settings_window import SettingsWindow
        from src.gui.components import ModernCard, ModernEntry, ModernCombobox
        from src.utils.rtl_support import rtl_support
        
        print("✅ جميع المكونات المطلوبة متوفرة")
        
        # اختبار إنشاء مكونات الإعدادات
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # اختبار إنشاء المكونات
        test_frame = ttk.Frame(root)
        
        # اختبار البطاقات
        card = ModernCard(test_frame, title="اختبار")
        print("✅ إنشاء البطاقات يعمل")
        
        # اختبار حقول الإدخال
        entry = ModernEntry(test_frame, "اختبار", placeholder="نص تجريبي")
        print("✅ إنشاء حقول الإدخال يعمل")
        
        # اختبار القوائم المنسدلة
        combo = ModernCombobox(test_frame, "اختبار", values=["خيار 1", "خيار 2"])
        print("✅ إنشاء القوائم المنسدلة يعمل")
        
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لاختبار الإعدادات"""
    print("🚀 بدء اختبار نافذة الإعدادات...")
    print("=" * 60)
    
    # اختبار الميزات
    features_ok = test_settings_features()
    
    # اختبار التكامل
    integration_ok = test_settings_integration()
    
    if features_ok and integration_ok:
        print("\n🖥️ سيتم فتح نافذة اختبار الإعدادات...")
        print("تحقق من الميزات التالية:")
        print("1. التبويبات المختلفة (عام، مظهر، قاعدة بيانات، إلخ)")
        print("2. تغيير الثيمات ومعاينتها فوراً")
        print("3. إعدادات المدرسة والنظام")
        print("4. إدارة قاعدة البيانات")
        print("5. حفظ وتطبيق الإعدادات")
        print("6. إعادة تعيين الإعدادات الافتراضية")
        print("7. واجهة RTL محسنة")
        print("\nأغلق النافذة للمتابعة...")
        
        # اختبار النافذة
        settings_ok = test_settings_window()
        
        if settings_ok:
            print("\n🎉 اختبار نافذة الإعدادات مكتمل!")
        else:
            print("\n⚠️ فشل في اختبار نافذة الإعدادات")
    else:
        print("\n⚠️ مشكلة في المتطلبات الأساسية")

if __name__ == "__main__":
    main()
