#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة إضافة الطالب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_students_management():
    """تشخيص نافذة إدارة الطلاب"""
    try:
        from src.gui.students_management import StudentsManagementWindow
        from src.utils.themes import theme_manager
        
        print("🔍 بدء تشخيص نافذة إدارة الطلاب...")
        
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.title("تشخيص إضافة الطالب")
        root.geometry("400x300")
        
        # تطبيق الثيم
        style = ttk.Style()
        theme_manager.apply_theme_to_style(style)
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🔧 تشخيص إضافة الطالب", 
                               font=("Tahoma", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # متغير لحفظ نافذة الطلاب
        students_window = None
        
        def open_students_window():
            nonlocal students_window
            try:
                print("📂 فتح نافذة إدارة الطلاب...")
                students_window = StudentsManagementWindow(root)
                print("✅ تم فتح النافذة بنجاح")
                
                # التحقق من وجود المكونات
                if hasattr(students_window, 'student_entries'):
                    print("✅ student_entries موجود")
                    print(f"   المفاتيح: {list(students_window.student_entries.keys())}")
                else:
                    print("❌ student_entries غير موجود")
                
                if hasattr(students_window, 'address_text'):
                    print("✅ address_text موجود")
                else:
                    print("❌ address_text غير موجود")
                    
            except Exception as e:
                print(f"❌ خطأ في فتح النافذة: {str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("خطأ", f"خطأ في فتح النافذة: {str(e)}")
        
        def test_add_student():
            if students_window is None:
                messagebox.showwarning("تحذير", "يرجى فتح نافذة إدارة الطلاب أولاً")
                return
            
            try:
                print("🧪 اختبار إضافة طالب...")
                
                # ملء البيانات التجريبية
                students_window.student_entries['student_id'].set('12345')
                students_window.student_entries['name'].set('أحمد محمد علي')
                students_window.student_entries['grade'].set('الثاني متوسط')
                students_window.student_entries['class_section'].set('أ')
                students_window.student_entries['phone'].set('0501234567')
                students_window.student_entries['parent_phone'].set('0507654321')
                
                # ملء العنوان
                students_window.address_text.delete('1.0', tk.END)
                students_window.address_text.insert('1.0', 'الرياض، حي النخيل')
                
                print("📝 تم ملء البيانات التجريبية")
                
                # محاولة إضافة الطالب
                students_window.add_student()
                
            except Exception as e:
                print(f"❌ خطأ في اختبار إضافة الطالب: {str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("خطأ", f"خطأ في الاختبار: {str(e)}")
        
        def test_validation():
            if students_window is None:
                messagebox.showwarning("تحذير", "يرجى فتح نافذة إدارة الطلاب أولاً")
                return
            
            try:
                print("🔍 اختبار التحقق من البيانات...")
                
                # اختبار بيانات فارغة
                for key in students_window.student_entries:
                    students_window.student_entries[key].set('')
                
                errors = students_window.validate_student_data()
                print(f"أخطاء البيانات الفارغة: {errors}")
                
                # اختبار بيانات صحيحة
                students_window.student_entries['student_id'].set('12345')
                students_window.student_entries['name'].set('أحمد محمد علي')
                students_window.student_entries['grade'].set('الثاني متوسط')
                students_window.student_entries['class_section'].set('أ')
                
                errors = students_window.validate_student_data()
                print(f"أخطاء البيانات الصحيحة: {errors}")
                
                messagebox.showinfo("نتيجة", f"تم اختبار التحقق. راجع وحدة التحكم للتفاصيل.")
                
            except Exception as e:
                print(f"❌ خطأ في اختبار التحقق: {str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("خطأ", f"خطأ في اختبار التحقق: {str(e)}")
        
        # أزرار التشخيص
        ttk.Button(main_frame, text="📂 فتح نافذة إدارة الطلاب", 
                  command=open_students_window).pack(pady=5)
        
        ttk.Button(main_frame, text="🔍 اختبار التحقق من البيانات", 
                  command=test_validation).pack(pady=5)
        
        ttk.Button(main_frame, text="🧪 اختبار إضافة طالب", 
                  command=test_add_student).pack(pady=5)
        
        # معلومات
        info_label = ttk.Label(main_frame, 
                              text="راقب وحدة التحكم للحصول على تفاصيل التشخيص", 
                              font=("Tahoma", 10))
        info_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
        print("✅ تم إنشاء نافذة التشخيص")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_students_management()
