#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات واجهة المستخدم المحسنة
يحتوي على مكونات مخصصة وقابلة لإعادة الاستخدام
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, date
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.rtl_support import rtl_support
from src.utils.themes import theme_manager

class ModernCard(ttk.Frame):
    """بطاقة حديثة مع تصميم محسن"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, style='Card.TFrame', **kwargs)
        self.title = title
        self.create_card()
    
    def create_card(self):
        """إنشاء البطاقة"""
        self.columnconfigure(0, weight=1)
        
        if self.title:
            # عنوان البطاقة
            title_frame = ttk.Frame(self)
            title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=15, pady=(15, 5))
            title_frame.columnconfigure(0, weight=1)
            
            title_label = rtl_support.create_rtl_label(title_frame, text=self.title, style='Header.TLabel')
            title_label.grid(row=0, column=0, sticky=tk.E)
            
            # خط فاصل
            separator = ttk.Separator(self, orient='horizontal')
            separator.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=15, pady=5)
            
            # منطقة المحتوى
            self.content_frame = ttk.Frame(self)
            self.content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=15, pady=(5, 15))
            self.content_frame.columnconfigure(0, weight=1)
            self.rowconfigure(2, weight=1)
        else:
            # بدون عنوان
            self.content_frame = ttk.Frame(self)
            self.content_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=15, pady=15)
            self.content_frame.columnconfigure(0, weight=1)
            self.rowconfigure(0, weight=1)
    
    def add_content(self, widget, **grid_options):
        """إضافة محتوى للبطاقة"""
        widget.grid(in_=self.content_frame, **grid_options)

class StatCard(ModernCard):
    """بطاقة إحصائية"""
    
    def __init__(self, parent, title, value, icon=None, color="primary", **kwargs):
        self.stat_value = value
        self.icon = icon
        self.color = color
        super().__init__(parent, title, **kwargs)
        self.create_stat_content()
    
    def create_stat_content(self):
        """إنشاء محتوى الإحصائية"""
        # القيمة الرئيسية
        value_label = rtl_support.create_rtl_label(
            self.content_frame, 
            text=str(self.stat_value),
            font=(theme_manager.get_font("header")[0], 24, "bold")
        )
        value_label.grid(row=0, column=0, pady=(10, 5))
        
        # أيقونة (إذا توفرت)
        if self.icon:
            icon_label = rtl_support.create_rtl_label(self.content_frame, text=self.icon, font=("Arial", 16))
            icon_label.grid(row=1, column=0, pady=5)
    
    def update_value(self, new_value):
        """تحديث القيمة"""
        self.stat_value = new_value
        # تحديث النص في التسمية

class ModernButton(ttk.Button):
    """زر محسن مع تأثيرات"""

    def __init__(self, parent, text, command=None, style="TButton", icon=None, **kwargs):
        self.icon = icon
        # إذا كان النص يحتوي على أيقونة بالفعل، لا نضيف أيقونة إضافية
        if icon and not any(emoji in text for emoji in ['➕', '✏️', '🗑️', '🧹', '🔄', '📊', '📄', '📥', '🖨️', '❌', '⚙️', '🎓', '📝', '👥', '📅', '🔍', '💾', '🌙', '❓']):
            display_text = f"{icon} {text}"
        else:
            display_text = text

        super().__init__(parent, text=display_text, command=command, style=style, **kwargs)
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)

    def on_enter(self, event):
        """عند دخول الماوس"""
        self.configure(cursor="hand2")

    def on_leave(self, event):
        """عند خروج الماوس"""
        self.configure(cursor="")

class ModernEntry(ttk.Frame):
    """حقل إدخال محسن مع تسمية"""
    
    def __init__(self, parent, label_text, textvariable=None, placeholder="", **kwargs):
        super().__init__(parent)
        self.label_text = label_text
        self.textvariable = textvariable or tk.StringVar()
        self.placeholder = placeholder
        self.create_entry()
    
    def create_entry(self):
        """إنشاء حقل الإدخال"""
        self.columnconfigure(0, weight=1)
        
        # التسمية
        label = rtl_support.create_rtl_label(self, text=self.label_text, style='Info.TLabel')
        label.grid(row=0, column=0, sticky=tk.E, pady=(0, 5))
        
        # حقل الإدخال
        self.entry = rtl_support.create_rtl_entry(self, textvariable=self.textvariable)
        self.entry.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # إضافة placeholder
        if self.placeholder:
            self.add_placeholder()
    
    def add_placeholder(self):
        """إضافة نص توضيحي"""
        def on_focus_in(event):
            if self.textvariable.get() == self.placeholder:
                self.textvariable.set("")
                self.entry.configure(foreground=theme_manager.get_color("text_primary"))
        
        def on_focus_out(event):
            if not self.textvariable.get():
                self.textvariable.set(self.placeholder)
                self.entry.configure(foreground=theme_manager.get_color("text_secondary"))
        
        self.entry.bind("<FocusIn>", on_focus_in)
        self.entry.bind("<FocusOut>", on_focus_out)
        
        # تعيين النص الافتراضي
        if not self.textvariable.get():
            self.textvariable.set(self.placeholder)
            self.entry.configure(foreground=theme_manager.get_color("text_secondary"))
    
    def get(self):
        """الحصول على القيمة"""
        value = self.textvariable.get()
        # إذا كانت القيمة هي placeholder أو فارغة، نرجع نص فارغ
        if value == self.placeholder or not value:
            return ""
        return value.strip()
    
    def set(self, value):
        """تعيين القيمة"""
        if value and value.strip():
            self.textvariable.set(value.strip())
            self.entry.configure(foreground=theme_manager.get_color("text_primary"))
        else:
            # إذا كانت القيمة فارغة، نعرض placeholder
            if self.placeholder:
                self.textvariable.set(self.placeholder)
                self.entry.configure(foreground=theme_manager.get_color("text_secondary"))
            else:
                self.textvariable.set("")

class ModernCombobox(ttk.Frame):
    """قائمة منسدلة محسنة مع تسمية"""
    
    def __init__(self, parent, label_text, values=None, textvariable=None, **kwargs):
        super().__init__(parent)
        self.label_text = label_text
        self.values = values or []
        self.textvariable = textvariable or tk.StringVar()
        self.create_combobox()
    
    def create_combobox(self):
        """إنشاء القائمة المنسدلة"""
        self.columnconfigure(0, weight=1)
        
        # التسمية
        label = rtl_support.create_rtl_label(self, text=self.label_text, style='Info.TLabel')
        label.grid(row=0, column=0, sticky=tk.E, pady=(0, 5))
        
        # القائمة المنسدلة
        self.combobox = ttk.Combobox(self, textvariable=self.textvariable, 
                                   values=self.values, state="readonly", justify='right')
        self.combobox.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def get(self):
        """الحصول على القيمة"""
        value = self.textvariable.get()
        return value.strip() if value else ""

    def set(self, value):
        """تعيين القيمة"""
        if value and value.strip():
            self.textvariable.set(value.strip())
        else:
            self.textvariable.set("")

class ModernTreeview(ttk.Frame):
    """جدول محسن مع بحث وتصفية"""
    
    def __init__(self, parent, columns, **kwargs):
        super().__init__(parent)
        self.columns = columns
        self.data = []
        self.filtered_data = []
        self.create_treeview()
    
    def create_treeview(self):
        """إنشاء الجدول"""
        self.columnconfigure(0, weight=1)
        self.rowconfigure(1, weight=1)
        
        # شريط البحث
        search_frame = ttk.Frame(self)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.columnconfigure(1, weight=1)
        
        rtl_support.create_rtl_label(search_frame, text="البحث:").grid(row=0, column=0, padx=(0, 10), sticky=tk.E)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = rtl_support.create_rtl_entry(search_frame, textvariable=self.search_var)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # الجدول
        tree_frame = ttk.Frame(self)
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(1, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        self.tree = rtl_support.create_rtl_treeview(tree_frame, columns=self.columns, show='headings')
        
        # تكوين الأعمدة
        for col in self.columns:
            self.tree.heading(col, text=col, anchor='e')
            self.tree.column(col, anchor='e', width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.grid(row=0, column=0, sticky=(tk.N, tk.S))
        self.tree.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def on_search(self, *args):
        """البحث في البيانات"""
        search_term = self.search_var.get().lower()
        if not search_term:
            self.filtered_data = self.data.copy()
        else:
            self.filtered_data = [
                row for row in self.data 
                if any(search_term in str(cell).lower() for cell in row)
            ]
        self.refresh_display()
    
    def set_data(self, data):
        """تعيين البيانات"""
        self.data = data
        self.filtered_data = data.copy()
        self.refresh_display()
    
    def refresh_display(self):
        """تحديث العرض"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات المفلترة
        for row in self.filtered_data:
            self.tree.insert('', 'end', values=row)
    
    def get_selected(self):
        """الحصول على العنصر المحدد"""
        selection = self.tree.selection()
        if selection:
            return self.tree.item(selection[0])['values']
        return None

class LoadingDialog:
    """نافذة تحميل"""
    
    def __init__(self, parent, message="جاري التحميل..."):
        self.parent = parent
        self.message = message
        self.dialog = None
        self.create_dialog()
    
    def create_dialog(self):
        """إنشاء نافذة التحميل"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("تحميل")
        self.dialog.geometry("300x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (150 // 2)
        self.dialog.geometry(f"300x150+{x}+{y}")
        
        # المحتوى
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # الرسالة
        message_label = rtl_support.create_rtl_label(main_frame, text=self.message, style='Header.TLabel')
        message_label.pack(pady=(20, 10))
        
        # شريط التقدم
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=10)
        self.progress.start()
    
    def close(self):
        """إغلاق نافذة التحميل"""
        if self.dialog:
            self.progress.stop()
            self.dialog.destroy()
            self.dialog = None

class ConfirmDialog:
    """نافذة تأكيد"""
    
    def __init__(self, parent, title, message, confirm_text="تأكيد", cancel_text="إلغاء"):
        self.parent = parent
        self.title = title
        self.message = message
        self.confirm_text = confirm_text
        self.cancel_text = cancel_text
        self.result = False
        self.create_dialog()
    
    def create_dialog(self):
        """إنشاء نافذة التأكيد"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (200 // 2)
        self.dialog.geometry(f"400x200+{x}+{y}")
        
        # المحتوى
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # الرسالة
        message_label = rtl_support.create_rtl_label(main_frame, text=self.message, style='Info.TLabel')
        message_label.grid(row=0, column=0, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        confirm_btn = ModernButton(buttons_frame, text=self.confirm_text, 
                                 command=self.confirm, style="Success.TButton")
        confirm_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        cancel_btn = ModernButton(buttons_frame, text=self.cancel_text, 
                                command=self.cancel, style="TButton")
        cancel_btn.pack(side=tk.RIGHT)
    
    def confirm(self):
        """تأكيد"""
        self.result = True
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة والانتظار"""
        self.dialog.wait_window()
        return self.result
