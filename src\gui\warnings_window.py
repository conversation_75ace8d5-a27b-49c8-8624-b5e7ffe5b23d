#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الإنذارات
تعرض وتدير إنذارات الطلاب حسب أيام الغياب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.warnings_manager import WarningsManager
from src.utils.config import Config
from src.gui.components import ModernCard, ModernButton, LoadingDialog

class WarningsWindow:
    """نافذة إدارة الإنذارات"""
    
    def __init__(self, parent=None):
        """تهيئة نافذة الإنذارات"""
        self.parent = parent
        self.warnings_manager = WarningsManager()
        self.window = None
        self.warnings_data = []
        self.create_window()
    
    def create_window(self):
        """إنشاء نافذة الإنذارات"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("⚠️ إدارة إنذارات الطلاب")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_content()
        
        # تحميل البيانات
        self.refresh_warnings()
    
    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # شريط الأدوات
        self.create_toolbar(main_frame)
        
        # دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # تبويب الإنذارات النشطة
        self.create_active_warnings_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # عنوان النافذة
        title_label = ttk.Label(toolbar_frame, text="⚠️ إدارة إنذارات الطلاب", 
                               font=("Tahoma", 16, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # أزرار الأدوات
        buttons_frame = ttk.Frame(toolbar_frame)
        buttons_frame.grid(row=0, column=1, sticky=tk.E)
        
        ttk.Button(buttons_frame, text="🔄 تحديث", 
                  command=self.refresh_warnings).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(buttons_frame, text="🔍 فحص تلقائي", 
                  command=self.auto_check_warnings).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(buttons_frame, text="📊 تقرير الإنذارات", 
                  command=self.generate_warnings_report).pack(side=tk.RIGHT, padx=5)
        
        toolbar_frame.columnconfigure(1, weight=1)
    
    def create_active_warnings_tab(self):
        """إنشاء تبويب الإنذارات النشطة"""
        warnings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(warnings_frame, text="⚠️ الإنذارات النشطة")
        warnings_frame.columnconfigure(0, weight=1)
        warnings_frame.rowconfigure(1, weight=1)
        
        # معلومات سريعة
        info_frame = ttk.Frame(warnings_frame)
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(0, weight=1)
        
        self.info_label = ttk.Label(info_frame, text="جاري تحميل البيانات...", 
                                   font=("Tahoma", 10))
        self.info_label.grid(row=0, column=0, sticky=tk.W)
        
        # جدول الإنذارات
        self.create_warnings_table(warnings_frame)
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(warnings_frame)
        actions_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(actions_frame, text="✅ حل الإنذار", 
                  command=self.resolve_selected_warning).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(actions_frame, text="📞 اتصال بولي الأمر", 
                  command=self.contact_parent).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(actions_frame, text="📝 إضافة ملاحظة", 
                  command=self.add_note).pack(side=tk.LEFT, padx=5)
    
    def create_warnings_table(self, parent):
        """إنشاء جدول الإنذارات"""
        # إطار الجدول مع شريط التمرير
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # إنشاء Treeview
        columns = ("student_name", "grade", "absence_days", "warning_level", 
                  "warning_date", "action_required")
        
        self.warnings_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف الأعمدة
        self.warnings_tree.heading("student_name", text="اسم الطالب")
        self.warnings_tree.heading("grade", text="الصف")
        self.warnings_tree.heading("absence_days", text="أيام الغياب")
        self.warnings_tree.heading("warning_level", text="مستوى الإنذار")
        self.warnings_tree.heading("warning_date", text="تاريخ الإنذار")
        self.warnings_tree.heading("action_required", text="الإجراء المطلوب")
        
        # تحديد عرض الأعمدة
        self.warnings_tree.column("student_name", width=150)
        self.warnings_tree.column("grade", width=100)
        self.warnings_tree.column("absence_days", width=100)
        self.warnings_tree.column("warning_level", width=120)
        self.warnings_tree.column("warning_date", width=100)
        self.warnings_tree.column("action_required", width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.warnings_tree.yview)
        self.warnings_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول وشريط التمرير
        self.warnings_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(stats_frame, text="📊 الإحصائيات")
        stats_frame.columnconfigure(0, weight=1)
        
        # بطاقات الإحصائيات
        cards_frame = ttk.Frame(stats_frame)
        cards_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        cards_frame.columnconfigure((0, 1, 2, 3), weight=1)
        
        # بطاقة الإنذارات النشطة
        self.active_warnings_card = ModernCard(cards_frame, "⚠️ إنذارات نشطة")
        self.active_warnings_card.grid(row=0, column=0, padx=5, sticky=(tk.W, tk.E))
        
        self.active_count_label = ttk.Label(self.active_warnings_card.content_frame, 
                                           text="0", font=("Tahoma", 20, "bold"))
        self.active_count_label.pack()
        
        # بطاقة الإنذارات المحلولة
        self.resolved_warnings_card = ModernCard(cards_frame, "✅ إنذارات محلولة")
        self.resolved_warnings_card.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        self.resolved_count_label = ttk.Label(self.resolved_warnings_card.content_frame, 
                                             text="0", font=("Tahoma", 20, "bold"))
        self.resolved_count_label.pack()
        
        # بطاقة الحالات الحرجة
        self.critical_warnings_card = ModernCard(cards_frame, "🚨 حالات حرجة")
        self.critical_warnings_card.grid(row=0, column=2, padx=5, sticky=(tk.W, tk.E))
        
        self.critical_count_label = ttk.Label(self.critical_warnings_card.content_frame, 
                                             text="0", font=("Tahoma", 20, "bold"), 
                                             foreground="red")
        self.critical_count_label.pack()
        
        # بطاقة معدل الحل
        self.resolution_rate_card = ModernCard(cards_frame, "📈 معدل الحل")
        self.resolution_rate_card.grid(row=0, column=3, padx=5, sticky=(tk.W, tk.E))
        
        self.resolution_rate_label = ttk.Label(self.resolution_rate_card.content_frame, 
                                              text="0%", font=("Tahoma", 20, "bold"))
        self.resolution_rate_label.pack()
        
        # جدول الطلاب الأكثر تغيباً
        top_students_frame = ttk.LabelFrame(stats_frame, text="الطلاب الأكثر تغيباً", padding="10")
        top_students_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        top_students_frame.columnconfigure(0, weight=1)
        
        # جدول الطلاب
        self.top_students_tree = ttk.Treeview(top_students_frame, 
                                             columns=("name", "warnings"), 
                                             show="headings", height=8)
        
        self.top_students_tree.heading("name", text="اسم الطالب")
        self.top_students_tree.heading("warnings", text="عدد الإنذارات")
        
        self.top_students_tree.column("name", width=200)
        self.top_students_tree.column("warnings", width=100)
        
        self.top_students_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        stats_frame.rowconfigure(1, weight=1)
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(settings_frame, text="⚙️ إعدادات الإنذارات")
        settings_frame.columnconfigure(0, weight=1)
        
        # إعدادات مستويات الإنذار
        levels_frame = ttk.LabelFrame(settings_frame, text="مستويات الإنذار", padding="10")
        levels_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        levels_frame.columnconfigure(1, weight=1)
        
        row = 0
        self.warning_entries = {}
        
        for level, config in Config.WARNING_LEVELS.items():
            # اسم المستوى
            ttk.Label(levels_frame, text=f"{config['name']}:", 
                     font=("Tahoma", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
            
            # عدد الأيام
            days_frame = ttk.Frame(levels_frame)
            days_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
            
            ttk.Label(days_frame, text="عدد الأيام:").pack(side=tk.LEFT)
            
            days_var = tk.StringVar(value=str(config['days']))
            days_entry = ttk.Entry(days_frame, textvariable=days_var, width=5)
            days_entry.pack(side=tk.LEFT, padx=5)
            
            ttk.Label(days_frame, text=f"الإجراء: {config['action']}").pack(side=tk.LEFT, padx=10)
            
            self.warning_entries[level] = days_var
            row += 1
        
        # أزرار الحفظ
        buttons_frame = ttk.Frame(settings_frame)
        buttons_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Button(buttons_frame, text="💾 حفظ الإعدادات", 
                  command=self.save_warning_settings).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="🔄 استعادة الافتراضي", 
                  command=self.reset_warning_settings).pack(side=tk.LEFT, padx=5)
    
    def refresh_warnings(self):
        """تحديث بيانات الإنذارات"""
        try:
            # تحميل الإنذارات النشطة
            self.warnings_data = self.warnings_manager.check_all_students_warnings()
            
            # تحديث الجدول
            self.update_warnings_table()
            
            # تحديث الإحصائيات
            self.update_statistics()
            
            # تحديث معلومات سريعة
            active_count = len([w for w in self.warnings_data if w.get('needs_warning', False)])
            self.info_label.config(text=f"تم العثور على {active_count} إنذار نشط")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {str(e)}")
    
    def update_warnings_table(self):
        """تحديث جدول الإنذارات"""
        # مسح البيانات الحالية
        for item in self.warnings_tree.get_children():
            self.warnings_tree.delete(item)
        
        # إضافة البيانات الجديدة
        for warning in self.warnings_data:
            if warning.get('needs_warning', False) and warning.get('warning_level'):
                level_info = warning['warning_level']
                
                # تحديد لون الصف حسب مستوى الإنذار
                tags = (level_info['level'],)
                
                self.warnings_tree.insert("", "end", values=(
                    warning.get('student_name', 'غير معروف'),
                    warning.get('student_grade', 'غير محدد'),
                    warning.get('absence_days', 0),
                    level_info['name'],
                    datetime.now().strftime('%Y-%m-%d'),
                    level_info['action']
                ), tags=tags)
        
        # تطبيق الألوان
        for level, config in Config.WARNING_LEVELS.items():
            self.warnings_tree.tag_configure(level, background=config['color'], foreground='white')
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            stats = self.warnings_manager.get_warnings_statistics()
            
            # تحديث البطاقات
            self.active_count_label.config(text=str(stats.get('active_warnings', 0)))
            self.resolved_count_label.config(text=str(stats.get('resolved_warnings', 0)))
            
            # الحالات الحرجة
            critical_count = stats.get('level_statistics', {}).get('critical', 0)
            self.critical_count_label.config(text=str(critical_count))
            
            # معدل الحل
            total = stats.get('total_warnings', 0)
            resolved = stats.get('resolved_warnings', 0)
            rate = (resolved / total * 100) if total > 0 else 0
            self.resolution_rate_label.config(text=f"{rate:.1f}%")
            
            # تحديث جدول الطلاب الأكثر تغيباً
            for item in self.top_students_tree.get_children():
                self.top_students_tree.delete(item)
            
            for student in stats.get('top_absent_students', []):
                self.top_students_tree.insert("", "end", values=(
                    student['name'], student['warnings']
                ))
                
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def auto_check_warnings(self):
        """فحص تلقائي للإنذارات"""
        try:
            loading = LoadingDialog(self.window, "جاري الفحص التلقائي...")
            
            result = self.warnings_manager.auto_check_and_create_warnings()
            
            loading.close()
            
            new_warnings = result.get('new_warnings', 0)
            total_checked = result.get('total_checked', 0)
            
            messagebox.showinfo("تم الفحص", 
                              f"تم فحص {total_checked} طالب\n"
                              f"تم إنشاء {new_warnings} إنذار جديد")
            
            self.refresh_warnings()
            
        except Exception as e:
            if 'loading' in locals():
                loading.close()
            messagebox.showerror("خطأ", f"خطأ في الفحص التلقائي: {str(e)}")
    
    def resolve_selected_warning(self):
        """حل الإنذار المحدد"""
        selection = self.warnings_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار إنذار لحله")
            return
        
        # نافذة إدخال ملاحظات الحل
        resolution_window = tk.Toplevel(self.window)
        resolution_window.title("حل الإنذار")
        resolution_window.geometry("400x200")
        resolution_window.transient(self.window)
        resolution_window.grab_set()
        
        ttk.Label(resolution_window, text="ملاحظات الحل:", font=("Tahoma", 10)).pack(pady=10)
        
        notes_text = tk.Text(resolution_window, height=5, width=40)
        notes_text.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        def save_resolution():
            notes = notes_text.get("1.0", tk.END).strip()
            # هنا يمكن إضافة منطق حل الإنذار
            messagebox.showinfo("تم", "تم حل الإنذار بنجاح")
            resolution_window.destroy()
            self.refresh_warnings()
        
        ttk.Button(resolution_window, text="حفظ", command=save_resolution).pack(pady=10)
    
    def contact_parent(self):
        """اتصال بولي الأمر"""
        selection = self.warnings_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب للاتصال بولي أمره")
            return
        
        messagebox.showinfo("معلومات", "ميزة الاتصال بولي الأمر قيد التطوير")
    
    def add_note(self):
        """إضافة ملاحظة"""
        selection = self.warnings_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار إنذار لإضافة ملاحظة")
            return
        
        messagebox.showinfo("معلومات", "ميزة إضافة الملاحظات قيد التطوير")
    
    def generate_warnings_report(self):
        """إنشاء تقرير الإنذارات"""
        messagebox.showinfo("معلومات", "ميزة تقرير الإنذارات قيد التطوير")
    
    def save_warning_settings(self):
        """حفظ إعدادات الإنذارات"""
        messagebox.showinfo("معلومات", "ميزة حفظ إعدادات الإنذارات قيد التطوير")
    
    def reset_warning_settings(self):
        """استعادة الإعدادات الافتراضية"""
        for level, config in Config.WARNING_LEVELS.items():
            if level in self.warning_entries:
                self.warning_entries[level].set(str(config['days']))
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

def open_warnings_window(parent=None):
    """فتح نافذة إدارة الإنذارات"""
    warnings_window = WarningsWindow(parent)
    return warnings_window

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    app = WarningsWindow()
    root.mainloop()
