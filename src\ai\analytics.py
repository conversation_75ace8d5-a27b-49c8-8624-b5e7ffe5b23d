#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرك التحليل والذكاء الصناعي
يحتوي على خوارزميات تحليل أنماط الغياب والتنبؤ
"""

import sys
import os
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import Config
from src.database.database_manager import DatabaseManager

try:
    import pandas as pd
    import numpy as np
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import LabelEncoder
    from sklearn.metrics import accuracy_score, classification_report
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    SKLEARN_AVAILABLE = True
    PANDAS_AVAILABLE = True
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    SKLEARN_AVAILABLE = False
    PANDAS_AVAILABLE = False
    MATPLOTLIB_AVAILABLE = False
    print(f"تحذير: بعض مكتبات الذكاء الصناعي غير متوفرة: {e}")

class AttendanceAnalytics:
    """فئة تحليل الغياب والذكاء الصناعي"""
    
    def __init__(self):
        """تهيئة محرك التحليل"""
        self.db_manager = DatabaseManager()
        self.model = None
        self.label_encoders = {}
        self.feature_columns = ['grade', 'day_of_week', 'month', 'semester', 'previous_absences']
    
    def get_attendance_data(self, days_back: int = 365) -> pd.DataFrame:
        """الحصول على بيانات الغياب للتحليل"""
        if not PANDAS_AVAILABLE:
            raise ImportError("مكتبة pandas غير متوفرة")
        
        try:
            # تاريخ البداية
            start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
            
            # الحصول على جميع الطلاب
            students = self.db_manager.get_students()
            
            # إنشاء قائمة البيانات
            data_list = []
            
            for student in students:
                # الحصول على غيابات الطالب
                absences = self.db_manager.get_student_absences(student['id'])
                
                for absence in absences:
                    if absence['absence_date'] >= start_date:
                        # تحويل التاريخ
                        absence_date = datetime.strptime(absence['absence_date'], "%Y-%m-%d")
                        
                        # حساب الغيابات السابقة
                        previous_absences = len([a for a in absences 
                                               if a['absence_date'] < absence['absence_date']])
                        
                        data_list.append({
                            'student_id': student['id'],
                            'student_name': student['name'],
                            'grade': student['grade'],
                            'absence_date': absence['absence_date'],
                            'absence_type': absence['absence_type'],
                            'day_of_week': absence_date.weekday(),
                            'month': absence_date.month,
                            'semester': self._get_semester_from_date(absence_date),
                            'previous_absences': previous_absences,
                            'reason': absence.get('reason', ''),
                            'is_absent': 1
                        })
            
            # تحويل إلى DataFrame
            df = pd.DataFrame(data_list)
            
            if df.empty:
                # إنشاء DataFrame فارغ بالأعمدة المطلوبة
                df = pd.DataFrame(columns=['student_id', 'student_name', 'grade', 'absence_date', 
                                         'absence_type', 'day_of_week', 'month', 'semester', 
                                         'previous_absences', 'reason', 'is_absent'])
            
            return df
            
        except Exception as e:
            raise Exception(f"خطأ في الحصول على بيانات الغياب: {str(e)}")
    
    def _get_semester_from_date(self, date_obj: datetime) -> int:
        """تحديد الفصل الدراسي من التاريخ"""
        month = date_obj.month
        
        if 9 <= month <= 12:
            return 1
        elif 1 <= month <= 4:
            return 2
        else:
            return 3
    
    def analyze_absence_patterns(self) -> Dict[str, Any]:
        """تحليل أنماط الغياب"""
        try:
            df = self.get_attendance_data()
            
            if df.empty:
                return {
                    'total_absences': 0,
                    'patterns': {},
                    'trends': {},
                    'recommendations': []
                }
            
            analysis = {}
            
            # إجمالي الغيابات
            analysis['total_absences'] = len(df)
            
            # أنماط الغياب
            patterns = {}
            
            # حسب اليوم
            patterns['by_day'] = df.groupby('day_of_week')['is_absent'].count().to_dict()
            
            # حسب الشهر
            patterns['by_month'] = df.groupby('month')['is_absent'].count().to_dict()
            
            # حسب الصف
            patterns['by_grade'] = df.groupby('grade')['is_absent'].count().to_dict()
            
            # حسب نوع الغياب
            patterns['by_type'] = df.groupby('absence_type')['is_absent'].count().to_dict()
            
            analysis['patterns'] = patterns
            
            # الاتجاهات الزمنية
            trends = {}
            
            # اتجاه شهري
            df['year_month'] = pd.to_datetime(df['absence_date']).dt.to_period('M')
            monthly_trend = df.groupby('year_month')['is_absent'].count()
            trends['monthly'] = monthly_trend.to_dict()
            
            analysis['trends'] = trends
            
            # التوصيات
            recommendations = self._generate_recommendations(patterns, trends)
            analysis['recommendations'] = recommendations
            
            return analysis
            
        except Exception as e:
            raise Exception(f"خطأ في تحليل أنماط الغياب: {str(e)}")
    
    def _generate_recommendations(self, patterns: Dict, trends: Dict) -> List[str]:
        """إنشاء توصيات بناءً على التحليل"""
        recommendations = []
        
        try:
            # تحليل الأيام
            if 'by_day' in patterns:
                day_counts = patterns['by_day']
                if day_counts:
                    max_day = max(day_counts, key=day_counts.get)
                    day_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
                    if max_day < len(day_names):
                        recommendations.append(f"أعلى معدل غياب في يوم {day_names[max_day]}. يُنصح بمراجعة الجدول الدراسي")
            
            # تحليل الشهور
            if 'by_month' in patterns:
                month_counts = patterns['by_month']
                if month_counts:
                    max_month = max(month_counts, key=month_counts.get)
                    month_names = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                    if max_month < len(month_names):
                        recommendations.append(f"أعلى معدل غياب في شهر {month_names[max_month]}. قد يحتاج لمتابعة خاصة")
            
            # تحليل نوع الغياب
            if 'by_type' in patterns:
                type_counts = patterns['by_type']
                if 'unexcused' in type_counts and type_counts['unexcused'] > 0:
                    total = sum(type_counts.values())
                    unexcused_ratio = type_counts['unexcused'] / total
                    if unexcused_ratio > 0.3:
                        recommendations.append("نسبة عالية من الغياب بدون عذر. يُنصح بتعزيز الانضباط المدرسي")
            
            if not recommendations:
                recommendations.append("لا توجد أنماط غياب مقلقة حالياً")
            
        except Exception as e:
            recommendations.append(f"خطأ في إنشاء التوصيات: {str(e)}")
        
        return recommendations
    
    def train_prediction_model(self) -> bool:
        """تدريب نموذج التنبؤ بالغياب"""
        if not SKLEARN_AVAILABLE:
            raise ImportError("مكتبة scikit-learn غير متوفرة")
        
        try:
            df = self.get_attendance_data()
            
            if df.empty or len(df) < Config.AI_MIN_DATA_POINTS:
                return False
            
            # إعداد البيانات
            X = df[self.feature_columns].copy()
            y = df['absence_type'].copy()
            
            # ترميز المتغيرات الفئوية
            for column in ['absence_type']:
                if column not in self.label_encoders:
                    self.label_encoders[column] = LabelEncoder()
                
                if column == 'absence_type':
                    y = self.label_encoders[column].fit_transform(y)
            
            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # تدريب النموذج
            self.model = RandomForestClassifier(n_estimators=100, random_state=42)
            self.model.fit(X_train, y_train)
            
            # تقييم النموذج
            y_pred = self.model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            print(f"دقة النموذج: {accuracy:.2f}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في تدريب النموذج: {str(e)}")
            return False
    
    def predict_absence_risk(self, student_id: int, target_date: str) -> Dict[str, Any]:
        """التنبؤ بخطر غياب طالب في تاريخ معين"""
        if not self.model:
            if not self.train_prediction_model():
                return {'risk_level': 'غير محدد', 'confidence': 0, 'message': 'لا توجد بيانات كافية للتنبؤ'}
        
        try:
            # الحصول على بيانات الطالب
            student = self.db_manager.get_student(student_id)
            if not student:
                return {'risk_level': 'غير محدد', 'confidence': 0, 'message': 'الطالب غير موجود'}
            
            # تحضير البيانات للتنبؤ
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")
            
            # حساب الغيابات السابقة
            absences = self.db_manager.get_student_absences(student_id)
            previous_absences = len([a for a in absences if a['absence_date'] < target_date])
            
            # إنشاء بيانات التنبؤ
            prediction_data = {
                'grade': student['grade'],
                'day_of_week': target_date_obj.weekday(),
                'month': target_date_obj.month,
                'semester': self._get_semester_from_date(target_date_obj),
                'previous_absences': previous_absences
            }
            
            # التنبؤ
            X_pred = pd.DataFrame([prediction_data])
            prediction = self.model.predict(X_pred)[0]
            probabilities = self.model.predict_proba(X_pred)[0]
            
            # تحويل التنبؤ إلى نص
            absence_types = list(self.label_encoders['absence_type'].classes_)
            predicted_type = absence_types[prediction]
            confidence = max(probabilities)
            
            # تحديد مستوى الخطر
            if predicted_type == 'unexcused':
                risk_level = 'عالي'
            elif predicted_type == 'excused':
                risk_level = 'متوسط'
            else:
                risk_level = 'منخفض'
            
            return {
                'risk_level': risk_level,
                'predicted_type': Config.ABSENCE_TYPES.get(predicted_type, predicted_type),
                'confidence': confidence,
                'message': f'احتمالية {Config.ABSENCE_TYPES.get(predicted_type, predicted_type)}: {confidence:.2f}'
            }
            
        except Exception as e:
            return {'risk_level': 'غير محدد', 'confidence': 0, 'message': f'خطأ في التنبؤ: {str(e)}'}
    
    def get_students_at_risk(self, days_ahead: int = 7) -> List[Dict[str, Any]]:
        """الحصول على قائمة الطلاب المعرضين لخطر الغياب"""
        try:
            students = self.db_manager.get_students()
            at_risk_students = []
            
            # التاريخ المستهدف
            target_date = (datetime.now() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
            
            for student in students:
                prediction = self.predict_absence_risk(student['id'], target_date)
                
                if prediction['risk_level'] in ['عالي', 'متوسط'] and prediction['confidence'] > 0.6:
                    at_risk_students.append({
                        'student_id': student['student_id'],
                        'student_name': student['name'],
                        'grade': Config.GRADES.get(student['grade'], str(student['grade'])),
                        'risk_level': prediction['risk_level'],
                        'confidence': prediction['confidence'],
                        'predicted_type': prediction.get('predicted_type', ''),
                        'target_date': target_date
                    })
            
            # ترتيب حسب مستوى الخطر والثقة
            at_risk_students.sort(key=lambda x: (x['risk_level'] == 'عالي', x['confidence']), reverse=True)
            
            return at_risk_students
            
        except Exception as e:
            print(f"خطأ في الحصول على الطلاب المعرضين للخطر: {str(e)}")
            return []
    
    def generate_insights_report(self) -> Dict[str, Any]:
        """إنشاء تقرير رؤى شامل"""
        try:
            # تحليل الأنماط
            patterns_analysis = self.analyze_absence_patterns()
            
            # الطلاب المعرضون للخطر
            at_risk_students = self.get_students_at_risk()
            
            # إحصائيات عامة
            stats = self.db_manager.get_absence_statistics()
            
            return {
                'patterns_analysis': patterns_analysis,
                'at_risk_students': at_risk_students,
                'general_statistics': stats,
                'generated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            return {
                'error': f"خطأ في إنشاء تقرير الرؤى: {str(e)}",
                'generated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

# إنشاء مثيل عام للاستخدام
analytics_engine = AttendanceAnalytics()
