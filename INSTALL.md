# دليل التثبيت - برنامج تسجيل غياب الطلاب الذكي

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM
- **مساحة القرص**: 500 MB مساحة فارغة

### الموصى به
- **Python**: الإصدار 3.10 أو أحدث
- **الذاكرة**: 8 GB RAM
- **مساحة القرص**: 2 GB مساحة فارغة

## خطوات التثبيت

### 1. تحقق من إصدار Python

```bash
python --version
```

يجب أن يكون الإصدار 3.8 أو أحدث. إذا لم يكن Python مثبتاً، قم بتحميله من [python.org](https://www.python.org/downloads/)

### 2. تحميل المشروع

```bash
# إذا كان لديك Git
git clone [repository-url]
cd PP1

# أو قم بتحميل الملف المضغوط واستخراجه
```

### 3. إنشاء بيئة افتراضية (اختياري لكن موصى به)

```bash
# إنشاء بيئة افتراضية
python -m venv attendance_env

# تفعيل البيئة الافتراضية
# على Windows:
attendance_env\Scripts\activate

# على macOS/Linux:
source attendance_env/bin/activate
```

### 4. تثبيت المتطلبات الأساسية

```bash
# تثبيت المكتبات الأساسية فقط
pip install pandas openpyxl tkcalendar
```

### 5. تثبيت المكتبات الاختيارية (للميزات المتقدمة)

```bash
# للرسوم البيانية والتحليلات
pip install matplotlib

# للذكاء الصناعي والتنبؤ
pip install scikit-learn

# لتصدير PDF
pip install reportlab

# لمعالجة الصور
pip install Pillow
```

أو تثبيت جميع المتطلبات دفعة واحدة:

```bash
pip install -r requirements.txt
```

### 6. اختبار التثبيت

```bash
python test_system.py
```

### 7. تشغيل البرنامج

```bash
python main.py
```

## حل المشاكل الشائعة

### مشكلة: `ModuleNotFoundError`

**السبب**: مكتبة غير مثبتة

**الحل**:
```bash
pip install [اسم المكتبة]
```

### مشكلة: `Permission denied` على Linux/macOS

**الحل**:
```bash
sudo pip install -r requirements.txt
```

أو استخدم البيئة الافتراضية:
```bash
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### مشكلة: خطأ في تشغيل البرنامج على Windows

**السبب**: مسار Python غير صحيح

**الحل**:
1. تأكد من إضافة Python إلى PATH
2. استخدم `py` بدلاً من `python`:
```bash
py main.py
```

### مشكلة: البرنامج لا يفتح

**السبب**: مشكلة في tkinter

**الحل على Linux**:
```bash
sudo apt-get install python3-tk
```

**الحل على macOS**:
```bash
brew install python-tk
```

### مشكلة: خطأ في قاعدة البيانات

**السبب**: صلاحيات الكتابة

**الحل**:
1. تأكد من وجود مجلد `data`
2. تأكد من صلاحيات الكتابة في مجلد المشروع

## التثبيت على أنظمة مختلفة

### Windows

1. تحميل Python من الموقع الرسمي
2. فتح Command Prompt أو PowerShell
3. اتباع الخطوات أعلاه

### macOS

```bash
# تثبيت Homebrew إذا لم يكن مثبتاً
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت Python
brew install python

# اتباع باقي الخطوات
```

### Linux (Ubuntu/Debian)

```bash
# تحديث النظام
sudo apt update

# تثبيت Python و pip
sudo apt install python3 python3-pip python3-venv python3-tk

# اتباع باقي الخطوات
```

### Linux (CentOS/RHEL)

```bash
# تثبيت Python
sudo yum install python3 python3-pip

# أو على الإصدارات الأحدث
sudo dnf install python3 python3-pip

# اتباع باقي الخطوات
```

## التحقق من التثبيت

بعد التثبيت، يجب أن تحصل على النتائج التالية:

### اختبار Python
```bash
python --version
# Python 3.8.0 أو أحدث
```

### اختبار المكتبات الأساسية
```python
import tkinter
import sqlite3
import pandas
import openpyxl
print("جميع المكتبات الأساسية متوفرة")
```

### اختبار البرنامج
```bash
python test_system.py
# يجب أن يظهر: "جميع الاختبارات نجحت!" أو معظمها
```

## الميزات حسب المكتبات المثبتة

| المكتبة | الميزات |
|---------|---------|
| pandas + openpyxl | الوظائف الأساسية، تصدير Excel |
| matplotlib | الرسوم البيانية والتحليلات البصرية |
| scikit-learn | الذكاء الصناعي والتنبؤ |
| reportlab | تصدير PDF |
| tkcalendar | اختيار التواريخ المحسن |

## نصائح للأداء الأمثل

1. **استخدم بيئة افتراضية** لتجنب تضارب المكتبات
2. **ثبت المكتبات الاختيارية** للحصول على جميع الميزات
3. **تأكد من وجود مساحة كافية** لقاعدة البيانات والتقارير
4. **قم بعمل نسخة احتياطية** من مجلد `data` بانتظام

## الدعم الفني

إذا واجهت مشاكل في التثبيت:

1. تأكد من اتباع جميع الخطوات بالترتيب
2. تحقق من رسائل الخطأ وابحث عن الحلول
3. جرب إعادة تثبيت المكتبات المشكلة
4. استخدم البيئة الافتراضية لتجنب التضارب

---

**ملاحظة**: البرنامج يعمل بالميزات الأساسية حتى لو لم تكن جميع المكتبات الاختيارية مثبتة.
