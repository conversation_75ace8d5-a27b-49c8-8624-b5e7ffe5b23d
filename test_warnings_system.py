#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الإنذارات
"""

import sys
import os
from datetime import datetime, timedelta
import sqlite3

# إضافة مسار المشروع
sys.path.append('src')

from utils.warnings_manager import WarningsManager
from utils.config import Config
from database.database_manager import DatabaseManager

def create_test_data():
    """إنشاء بيانات تجريبية للاختبار"""
    print("🔧 إنشاء بيانات تجريبية...")
    
    db_manager = DatabaseManager()
    
    # إنشاء طلاب تجريبيين
    test_students = [
        {"name": "أحمد محمد علي", "grade": "الأول متوسط", "section": "أ", "student_id": "1001"},
        {"name": "محمد أحمد سالم", "grade": "الثاني متوسط", "section": "ب", "student_id": "1002"},
        {"name": "علي حسن محمود", "grade": "الثالث متوسط", "section": "ج", "student_id": "1003"},
        {"name": "يوسف إبراهيم", "grade": "الأول متوسط", "section": "أ", "student_id": "1004"},
        {"name": "عبدالله محمد", "grade": "الثاني متوسط", "section": "ب", "student_id": "1005"}
    ]
    
    # إضافة الطلاب
    for student in test_students:
        try:
            db_manager.add_student(
                name=student["name"],
                grade=student["grade"],
                section=student["section"],
                student_id=student["student_id"]
            )
            print(f"✅ تم إضافة الطالب: {student['name']}")
        except Exception as e:
            print(f"⚠️ الطالب موجود بالفعل: {student['name']}")
    
    # إنشاء سجلات غياب تجريبية
    print("\n📝 إنشاء سجلات غياب تجريبية...")
    
    students = db_manager.get_students()
    if not students:
        print("❌ لا توجد طلاب في قاعدة البيانات")
        return
    
    # إنشاء غيابات متدرجة للاختبار
    test_absences = [
        # طالب بـ 2 أيام غياب (لا يحتاج إنذار)
        {"student_id": students[0]["id"], "days": 2},
        # طالب بـ 4 أيام غياب (إنذار أولي)
        {"student_id": students[1]["id"], "days": 4},
        # طالب بـ 6 أيام غياب (إنذار متوسط)
        {"student_id": students[2]["id"], "days": 6},
        # طالب بـ 8 أيام غياب (إنذار نهائي)
        {"student_id": students[3]["id"], "days": 8},
        # طالب بـ 12 يوم غياب (حالة حرجة)
        {"student_id": students[4]["id"], "days": 12}
    ]
    
    current_date = datetime.now().date()
    
    for absence_data in test_absences:
        student_id = absence_data["student_id"]
        days = absence_data["days"]
        
        for i in range(days):
            absence_date = current_date - timedelta(days=i+1)
            
            try:
                db_manager.add_attendance_record(
                    student_id=student_id,
                    date=absence_date.strftime("%Y-%m-%d"),
                    absence_type="unexcused",
                    reason="غياب بدون عذر"
                )
            except Exception as e:
                pass  # السجل موجود بالفعل
        
        student_name = next(s["name"] for s in students if s["id"] == student_id)
        print(f"✅ تم إنشاء {days} أيام غياب للطالب: {student_name}")

def test_warnings_system():
    """اختبار نظام الإنذارات"""
    print("\n🧪 اختبار نظام الإنذارات...")
    
    warnings_manager = WarningsManager()
    
    # اختبار فحص إنذارات جميع الطلاب
    print("\n1️⃣ فحص إنذارات جميع الطلاب:")
    all_warnings = warnings_manager.check_all_students_warnings()
    
    for warning in all_warnings:
        student_name = warning.get('student_name', 'غير معروف')
        absence_days = warning.get('absence_days', 0)
        warning_level = warning.get('warning_level')
        
        if warning_level:
            level_name = warning_level['name']
            level_color = warning_level['color']
            action = warning_level['action']
            
            print(f"⚠️ {student_name}: {absence_days} أيام غياب - {level_name} - {action}")
        else:
            print(f"✅ {student_name}: {absence_days} أيام غياب - لا يحتاج إنذار")
    
    # اختبار الفحص التلقائي وإنشاء الإنذارات
    print("\n2️⃣ الفحص التلقائي وإنشاء الإنذارات:")
    auto_result = warnings_manager.auto_check_and_create_warnings()
    
    total_checked = auto_result.get('total_checked', 0)
    new_warnings = auto_result.get('new_warnings', 0)
    
    print(f"📊 تم فحص {total_checked} طالب")
    print(f"🆕 تم إنشاء {new_warnings} إنذار جديد")
    
    # اختبار الإحصائيات
    print("\n3️⃣ إحصائيات الإنذارات:")
    stats = warnings_manager.get_warnings_statistics()
    
    print(f"📈 إجمالي الإنذارات: {stats.get('total_warnings', 0)}")
    print(f"⚠️ إنذارات نشطة: {stats.get('active_warnings', 0)}")
    print(f"✅ إنذارات محلولة: {stats.get('resolved_warnings', 0)}")
    
    # إحصائيات حسب المستوى
    level_stats = stats.get('level_statistics', {})
    print("\n📊 إحصائيات حسب المستوى:")
    for level, count in level_stats.items():
        if count > 0:
            level_name = Config.WARNING_LEVELS[level]['name']
            print(f"   {level_name}: {count}")
    
    # الطلاب الأكثر تغيباً
    top_students = stats.get('top_absent_students', [])
    if top_students:
        print("\n🔝 الطلاب الأكثر تغيباً:")
        for student in top_students[:5]:
            print(f"   {student['name']}: {student['warnings']} إنذار")
    
    return stats

def test_warning_levels():
    """اختبار مستويات الإنذار"""
    print("\n🎯 اختبار مستويات الإنذار:")
    
    warnings_manager = WarningsManager()
    
    test_cases = [1, 3, 5, 7, 10, 15]
    
    for days in test_cases:
        warning_level = warnings_manager.get_warning_level(days)
        
        if warning_level:
            print(f"📅 {days} أيام غياب → {warning_level['name']} ({warning_level['action']})")
        else:
            print(f"📅 {days} أيام غياب → لا يحتاج إنذار")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام الإنذارات")
    print("=" * 50)
    
    try:
        # إنشاء بيانات تجريبية
        create_test_data()
        
        # اختبار مستويات الإنذار
        test_warning_levels()
        
        # اختبار نظام الإنذارات
        stats = test_warnings_system()
        
        print("\n" + "=" * 50)
        print("✅ تم اختبار نظام الإنذارات بنجاح!")
        
        if stats.get('active_warnings', 0) > 0:
            print(f"⚠️ يوجد {stats['active_warnings']} إنذار نشط يحتاج متابعة")
        else:
            print("✅ لا توجد إنذارات نشطة")
        
        print("\n🎯 لاختبار الواجهة، شغل البرنامج الرئيسي وانقر على 'إدارة الإنذارات'")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
